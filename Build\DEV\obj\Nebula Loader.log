﻿  PORTABLE SECURITY BUILD SYSTEM
  ==============================
  Generating new BUILD_SIGNATURE_LEGACY: 0x7A715988
  Note: Portable system uses runtime entropy, this is just for fallback
  Successfully updated BUILD_SIGNATURE_LEGACY to: 0x7A715988
  
  Build signature update completed.
  debug.cpp
  config.cpp
  security.cpp
  core.cpp
  accessibility.cpp
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\utility(479,17): error C2512: 'ui::AccessibleTooltip::TooltipInfo::TooltipInfo': no appropriate default constructor available
  (compiling source file 'src/ui/accessibility.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\utility(479,17):
      while trying to match the argument list '()'
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\utility(479,17):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\accessibility.hpp(106,44):
          see reference to class template instantiation 'std::map<std::string,ui::AccessibleTooltip::TooltipInfo,std::less<std::string>,std::allocator<std::pair<const std::string,ui::AccessibleTooltip::TooltipInfo>>>' being compiled
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\map(345,18):
          while compiling class template member function 'ui::AccessibleTooltip::TooltipInfo &std::map<std::string,ui::AccessibleTooltip::TooltipInfo,std::less<std::string>,std::allocator<std::pair<const std::string,ui::AccessibleTooltip::TooltipInfo>>>::operator [](const std::basic_string<char,std::char_traits<char>,std::allocator<char>> &)'
              C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\accessibility.cpp(164,34):
              see the first reference to 'std::map<std::string,ui::AccessibleTooltip::TooltipInfo,std::less<std::string>,std::allocator<std::pair<const std::string,ui::AccessibleTooltip::TooltipInfo>>>::operator []' in 'ui::AccessibleTooltip::ShowTooltip'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\map(346,16):
          see reference to function template instantiation 'std::pair<std::_Tree_node<std::pair<const std::string,ui::AccessibleTooltip::TooltipInfo>,std::_Default_allocator_traits<_Alloc>::void_pointer> *,bool> std::map<std::string,ui::AccessibleTooltip::TooltipInfo,std::less<std::string>,_Alloc>::_Try_emplace<const std::basic_string<char,std::char_traits<char>,std::allocator<char>>&,>(_Keyty)' being compiled
          with
          [
              _Alloc=std::allocator<std::pair<const std::string,ui::AccessibleTooltip::TooltipInfo>>,
              _Keyty=const std::basic_string<char,std::char_traits<char>,std::allocator<char>> &
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\map(205,56):
          see reference to function template instantiation 'std::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<const std::string,ui::AccessibleTooltip::TooltipInfo>,std::_Default_allocator_traits<_Alloc>::void_pointer>>>::_Tree_temp_node<const std::piecewise_construct_t&,std::tuple<const std::basic_string<char,std::char_traits<char>,std::allocator<char>> &>,std::tuple<>>(_Alnode &,std::_Tree_node<std::pair<const std::string,ui::AccessibleTooltip::TooltipInfo>,std::_Default_allocator_traits<_Alloc>::void_pointer> *,const std::piecewise_construct_t &,std::tuple<const std::basic_string<char,std::char_traits<char>,std::allocator<char>> &> &&,std::tuple<> &&)' being compiled
          with
          [
              _Alloc=std::allocator<std::pair<const std::string,ui::AccessibleTooltip::TooltipInfo>>,
              _Alnode=std::allocator<std::_Tree_node<std::pair<const std::string,ui::AccessibleTooltip::TooltipInfo>,std::_Default_allocator_traits<std::allocator<std::pair<const std::string,ui::AccessibleTooltip::TooltipInfo>>>::void_pointer>>
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xtree(820,25):
          see reference to function template instantiation 'void std::_Default_allocator_traits<_Alloc>::construct<_Ty,const std::piecewise_construct_t&,std::tuple<const std::basic_string<char,std::char_traits<char>,std::allocator<char>> &>,std::tuple<>>(_Alloc &,_Objty *const ,const std::piecewise_construct_t &,std::tuple<const std::basic_string<char,std::char_traits<char>,std::allocator<char>> &> &&,std::tuple<> &&)' being compiled
          with
          [
              _Alloc=std::allocator<std::_Tree_node<std::pair<const std::string,ui::AccessibleTooltip::TooltipInfo>,std::_Default_allocator_traits<std::allocator<std::pair<const std::string,ui::AccessibleTooltip::TooltipInfo>>>::void_pointer>>,
              _Ty=std::pair<const std::string,ui::AccessibleTooltip::TooltipInfo>,
              _Objty=std::pair<const std::string,ui::AccessibleTooltip::TooltipInfo>
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xmemory(730,14):
          see reference to function template instantiation '_Ty *std::construct_at<_Objty,const std::piecewise_construct_t&,std::tuple<const std::basic_string<char,std::char_traits<char>,std::allocator<char>> &>,std::tuple<>>(_Ty *const ,const std::piecewise_construct_t &,std::tuple<const std::basic_string<char,std::char_traits<char>,std::allocator<char>> &> &&,std::tuple<> &&) noexcept(false)' being compiled
          with
          [
              _Ty=std::pair<const std::string,ui::AccessibleTooltip::TooltipInfo>,
              _Objty=std::pair<const std::string,ui::AccessibleTooltip::TooltipInfo>
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xutility(493,69):
          see reference to function template instantiation 'std::pair<const std::string,ui::AccessibleTooltip::TooltipInfo>::pair<const std::basic_string<char,std::char_traits<char>,std::allocator<char>>&,>(std::piecewise_construct_t,std::tuple<const std::basic_string<char,std::char_traits<char>,std::allocator<char>> &>,std::tuple<>)' being compiled
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\utility(331,15):
          see reference to function template instantiation 'std::pair<const std::string,ui::AccessibleTooltip::TooltipInfo>::pair<std::tuple<const std::basic_string<char,std::char_traits<char>,std::allocator<char>> &>,std::tuple<>,0,>(_Tuple1 &,_Tuple2 &,std::integer_sequence<size_t,0>,std::integer_sequence<size_t>)' being compiled
          with
          [
              _Tuple1=std::tuple<const std::basic_string<char,std::char_traits<char>,std::allocator<char>> &>,
              _Tuple2=std::tuple<>
          ]
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\utility(479,17): error C2512: 'ui::KeyboardNavigation::NavigableElement::NavigableElement': no appropriate default constructor available
  (compiling source file 'src/ui/accessibility.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\utility(479,17):
      while trying to match the argument list '()'
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\utility(479,17):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\accessibility.hpp(73,49):
          see reference to class template instantiation 'std::map<std::string,ui::KeyboardNavigation::NavigableElement,std::less<std::string>,std::allocator<std::pair<const std::string,ui::KeyboardNavigation::NavigableElement>>>' being compiled
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\map(345,18):
          while compiling class template member function 'ui::KeyboardNavigation::NavigableElement &std::map<std::string,ui::KeyboardNavigation::NavigableElement,std::less<std::string>,std::allocator<std::pair<const std::string,ui::KeyboardNavigation::NavigableElement>>>::operator [](const std::basic_string<char,std::char_traits<char>,std::allocator<char>> &)'
              C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\accessibility.cpp(16,32):
              see the first reference to 'std::map<std::string,ui::KeyboardNavigation::NavigableElement,std::less<std::string>,std::allocator<std::pair<const std::string,ui::KeyboardNavigation::NavigableElement>>>::operator []' in 'ui::KeyboardNavigation::RegisterElement'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\map(346,16):
          see reference to function template instantiation 'std::pair<std::_Tree_node<std::pair<const std::string,ui::KeyboardNavigation::NavigableElement>,std::_Default_allocator_traits<_Alloc>::void_pointer> *,bool> std::map<std::string,ui::KeyboardNavigation::NavigableElement,std::less<std::string>,_Alloc>::_Try_emplace<const std::basic_string<char,std::char_traits<char>,std::allocator<char>>&,>(_Keyty)' being compiled
          with
          [
              _Alloc=std::allocator<std::pair<const std::string,ui::KeyboardNavigation::NavigableElement>>,
              _Keyty=const std::basic_string<char,std::char_traits<char>,std::allocator<char>> &
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\map(205,56):
          see reference to function template instantiation 'std::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<const std::string,ui::KeyboardNavigation::NavigableElement>,std::_Default_allocator_traits<_Alloc>::void_pointer>>>::_Tree_temp_node<const std::piecewise_construct_t&,std::tuple<const std::basic_string<char,std::char_traits<char>,std::allocator<char>> &>,std::tuple<>>(_Alnode &,std::_Tree_node<std::pair<const std::string,ui::KeyboardNavigation::NavigableElement>,std::_Default_allocator_traits<_Alloc>::void_pointer> *,const std::piecewise_construct_t &,std::tuple<const std::basic_string<char,std::char_traits<char>,std::allocator<char>> &> &&,std::tuple<> &&)' being compiled
          with
          [
              _Alloc=std::allocator<std::pair<const std::string,ui::KeyboardNavigation::NavigableElement>>,
              _Alnode=std::allocator<std::_Tree_node<std::pair<const std::string,ui::KeyboardNavigation::NavigableElement>,std::_Default_allocator_traits<std::allocator<std::pair<const std::string,ui::KeyboardNavigation::NavigableElement>>>::void_pointer>>
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xtree(820,25):
          see reference to function template instantiation 'void std::_Default_allocator_traits<_Alloc>::construct<_Ty,const std::piecewise_construct_t&,std::tuple<const std::basic_string<char,std::char_traits<char>,std::allocator<char>> &>,std::tuple<>>(_Alloc &,_Objty *const ,const std::piecewise_construct_t &,std::tuple<const std::basic_string<char,std::char_traits<char>,std::allocator<char>> &> &&,std::tuple<> &&)' being compiled
          with
          [
              _Alloc=std::allocator<std::_Tree_node<std::pair<const std::string,ui::KeyboardNavigation::NavigableElement>,std::_Default_allocator_traits<std::allocator<std::pair<const std::string,ui::KeyboardNavigation::NavigableElement>>>::void_pointer>>,
              _Ty=std::pair<const std::string,ui::KeyboardNavigation::NavigableElement>,
              _Objty=std::pair<const std::string,ui::KeyboardNavigation::NavigableElement>
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xmemory(730,14):
          see reference to function template instantiation '_Ty *std::construct_at<_Objty,const std::piecewise_construct_t&,std::tuple<const std::basic_string<char,std::char_traits<char>,std::allocator<char>> &>,std::tuple<>>(_Ty *const ,const std::piecewise_construct_t &,std::tuple<const std::basic_string<char,std::char_traits<char>,std::allocator<char>> &> &&,std::tuple<> &&) noexcept(false)' being compiled
          with
          [
              _Ty=std::pair<const std::string,ui::KeyboardNavigation::NavigableElement>,
              _Objty=std::pair<const std::string,ui::KeyboardNavigation::NavigableElement>
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xutility(493,69):
          see reference to function template instantiation 'std::pair<const std::string,ui::KeyboardNavigation::NavigableElement>::pair<const std::basic_string<char,std::char_traits<char>,std::allocator<char>>&,>(std::piecewise_construct_t,std::tuple<const std::basic_string<char,std::char_traits<char>,std::allocator<char>> &>,std::tuple<>)' being compiled
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\utility(331,15):
          see reference to function template instantiation 'std::pair<const std::string,ui::KeyboardNavigation::NavigableElement>::pair<std::tuple<const std::basic_string<char,std::char_traits<char>,std::allocator<char>> &>,std::tuple<>,0,>(_Tuple1 &,_Tuple2 &,std::integer_sequence<size_t,0>,std::integer_sequence<size_t>)' being compiled
          with
          [
              _Tuple1=std::tuple<const std::basic_string<char,std::char_traits<char>,std::allocator<char>> &>,
              _Tuple2=std::tuple<>
          ]
  
  animation.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.hpp(124,28): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
  (compiling source file 'src/ui/animation.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.hpp(124,40): error C2143: syntax error: missing ',' before '&'
  (compiling source file 'src/ui/animation.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.hpp(128,32): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
  (compiling source file 'src/ui/animation.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.hpp(128,44): error C2143: syntax error: missing ',' before '&'
  (compiling source file 'src/ui/animation.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.hpp(134,32): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
  (compiling source file 'src/ui/animation.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.hpp(134,44): error C2143: syntax error: missing ',' before '&'
  (compiling source file 'src/ui/animation.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.hpp(138,42): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
  (compiling source file 'src/ui/animation.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.hpp(138,54): error C2143: syntax error: missing ',' before '&'
  (compiling source file 'src/ui/animation.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(253,28): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(253,40): error C2143: syntax error: missing ',' before '&'
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(259,66): error C2065: 'speed': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(261,13): error C2065: 'ImDrawList': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(261,25): error C2065: 'drawList': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(261,36): error C2653: 'ImGui': is not a class or namespace name
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(261,43): error C3861: 'GetWindowDrawList': identifier not found
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(271,24): error C2146: syntax error: missing ';' before identifier 'p1'
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(271,27): error C2065: 'center': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(271,57): error C2065: 'radius': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(271,65): error C2065: 'center': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(271,95): error C2065: 'radius': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(271,24): error C3861: 'p1': identifier not found
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(272,24): error C2146: syntax error: missing ';' before identifier 'p2'
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(272,27): error C2065: 'center': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(272,57): error C2065: 'radius': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(272,65): error C2065: 'center': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(272,95): error C2065: 'radius': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(272,24): error C3861: 'p2': identifier not found
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(275,17): error C2065: 'ImVec4': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(275,24): error C2146: syntax error: missing ';' before identifier 'segmentColor'
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(275,24): error C2065: 'segmentColor': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(275,39): error C2065: 'color': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(276,17): error C2065: 'segmentColor': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(278,17): error C2065: 'drawList': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(278,35): error C2065: 'p1': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(278,39): error C2065: 'p2': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(278,43): error C2653: 'ImGui': is not a class or namespace name
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(278,74): error C2065: 'segmentColor': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(278,50): error C3861: 'ColorConvertFloat4ToU32': identifier not found
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(278,89): error C2065: 'thickness': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(282,32): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(282,44): error C2143: syntax error: missing ',' before '&'
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(284,13): error C2065: 'ImDrawList': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(284,25): error C2065: 'drawList': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(284,36): error C2653: 'ImGui': is not a class or namespace name
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(284,43): error C3861: 'GetWindowDrawList': identifier not found
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(287,13): error C2065: 'drawList': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(287,37): error C2065: 'position': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(287,54): error C2065: 'position': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(287,67): error C2065: 'size': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(287,75): error C2065: 'position': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(287,88): error C2065: 'size': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(287,47): error C2064: term does not evaluate to a function taking 1 arguments
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(288,35): error C2653: 'ImGui': is not a class or namespace name
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(288,66): error C2065: 'backgroundColor': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(288,42): error C3861: 'ColorConvertFloat4ToU32': identifier not found
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(292,17): error C2065: 'animated': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(298,70): error C2065: 'progress': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(300,36): error C2065: 'progress': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(303,35): error C2065: 'size': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(305,17): error C2065: 'drawList': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(305,41): error C2065: 'position': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(305,58): error C2065: 'position': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(305,86): error C2065: 'position': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(305,99): error C2065: 'size': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(305,51): error C2064: term does not evaluate to a function taking 1 arguments
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(306,39): error C2653: 'ImGui': is not a class or namespace name
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(306,70): error C2065: 'progressColor': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(306,46): error C3861: 'ColorConvertFloat4ToU32': identifier not found
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(310,17): error C2065: 'animated': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(315,62): error C2065: 'size': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(316,17): error C2065: 'ImVec4': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(316,24): error C2146: syntax error: missing ';' before identifier 'shimmerColor'
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(316,24): error C2065: 'shimmerColor': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(316,39): error C2065: 'progressColor': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(317,17): error C2065: 'shimmerColor': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(319,17): error C2065: 'drawList': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(319,48): error C2065: 'position': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(319,78): error C2065: 'position': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(319,41): error C2064: term does not evaluate to a function taking 1 arguments
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(320,46): error C2065: 'position': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(320,76): error C2065: 'position': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(320,89): error C2065: 'size': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(320,39): error C2064: term does not evaluate to a function taking 1 arguments
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(321,39): error C2653: 'ImGui': is not a class or namespace name
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(321,70): error C2065: 'shimmerColor': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(321,46): error C3861: 'ColorConvertFloat4ToU32': identifier not found
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(325,32): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(325,44): error C2143: syntax error: missing ',' before '&'
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(331,73): error C2065: 'speed': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(334,39): error C2065: 'baseText': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(339,13): error C2653: 'ImGui': is not a class or namespace name
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(339,33): error C2065: 'position': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(339,20): error C3861: 'SetCursorPos': identifier not found
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(340,13): error C2653: 'ImGui': is not a class or namespace name
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(340,32): error C2065: 'color': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(340,20): error C3861: 'TextColored': identifier not found
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(343,42): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(343,54): error C2143: syntax error: missing ',' before '&'
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(349,13): error C2653: 'ImGui': is not a class or namespace name
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(349,33): error C2065: 'position': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(349,20): error C3861: 'SetCursorPos': identifier not found
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(352,31): error C2065: 'position': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(352,41): error C2065: 'scanType': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(352,51): error C3861: 'ImVec4': identifier not found
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(355,20): error C2146: syntax error: missing ';' before identifier 'progressPos'
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(355,32): error C2065: 'position': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(355,44): error C2065: 'position': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(355,20): error C3861: 'progressPos': identifier not found
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(356,20): error C2146: syntax error: missing ';' before identifier 'progressSize'
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\animation.cpp(356,20): error C1003: error count exceeds 100; stopping compilation
  components.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.hpp(137,41): error C2653: 'SecurityActionTrigger': is not a class or namespace name
  (compiling source file 'src/ui/components.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.hpp(137,35): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
  (compiling source file 'src/ui/components.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.hpp(137,77): error C2143: syntax error: missing ',' before '&'
  (compiling source file 'src/ui/components.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\components.cpp(27,14): error C1075: '{': no matching token found
  performance_monitor.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\performance_monitor.cpp(102,28): warning C4244: 'initializing': conversion from 'ULONGLONG' to 'double', possible loss of data
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\performance_monitor.cpp(168,34): error C2589: '(': illegal token on right side of '::'
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\performance_monitor.cpp(168,34): error C2059: syntax error: ')'
  security_integration.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.hpp(137,41): error C2653: 'SecurityActionTrigger': is not a class or namespace name
  (compiling source file 'src/ui/security_integration.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.hpp(137,35): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
  (compiling source file 'src/ui/security_integration.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.hpp(137,77): error C2143: syntax error: missing ',' before '&'
  (compiling source file 'src/ui/security_integration.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.cpp(260,17): error C2664: 'void ui::SecurityIntegration::HandleSecurityAction(const int)': cannot convert argument 1 from 'const ui::SecurityActionTrigger::ActionRequest' to 'const int'
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.cpp(260,38):
      No user-defined-conversion operator available that can perform this conversion, or the operator cannot be called
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.hpp(137,14):
      see declaration of 'ui::SecurityIntegration::HandleSecurityAction'
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.cpp(260,17):
      while trying to match the argument list '(const ui::SecurityActionTrigger::ActionRequest)'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.cpp(570,31): error C2511: 'void ui::SecurityIntegration::HandleSecurityAction(const ui::SecurityActionTrigger::ActionRequest &)': overloaded member function not found in 'ui::SecurityIntegration'
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.hpp(71,11):
      see declaration of 'ui::SecurityIntegration'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.cpp(577,25): error C2352: 'ui::SecurityIntegration::RunAllSecurityChecks': a call of a non-static member function requires an object
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.cpp(231,31):
      see declaration of 'ui::SecurityIntegration::RunAllSecurityChecks'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.cpp(588,25): error C2352: 'ui::SecurityIntegration::RunSecurityCheck': a call of a non-static member function requires an object
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.cpp(166,31):
      see declaration of 'ui::SecurityIntegration::RunSecurityCheck'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.cpp(593,21): error C2352: 'ui::SecurityIntegration::StopRealTimeMonitoring': a call of a non-static member function requires an object
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.cpp(270,31):
      see declaration of 'ui::SecurityIntegration::StopRealTimeMonitoring'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.cpp(594,21): error C2352: 'ui::SecurityIntegration::StartRealTimeMonitoring': a call of a non-static member function requires an object
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.cpp(244,31):
      see declaration of 'ui::SecurityIntegration::StartRealTimeMonitoring'
  
  test_performance_accessibility.cpp
  test_runner.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.hpp(137,41): error C2653: 'SecurityActionTrigger': is not a class or namespace name
  (compiling source file 'src/ui/test_runner.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.hpp(137,35): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
  (compiling source file 'src/ui/test_runner.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.hpp(137,77): error C2143: syntax error: missing ',' before '&'
  (compiling source file 'src/ui/test_runner.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(223,40): error C2039: 'GenerateRecommendations': is not a member of 'ui::SecurityStatusSummary'
  (compiling source file 'src/ui/test_runner.cpp')
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\components.hpp(192,11):
      see declaration of 'ui::SecurityStatusSummary'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(225,9): error C3536: 'recommendations': cannot be used before it is initialized
  (compiling source file 'src/ui/test_runner.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(229,32): error C3312: no callable 'begin' function found for type 'int'
  (compiling source file 'src/ui/test_runner.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(229,32): error C3312: no callable 'end' function found for type 'int'
  (compiling source file 'src/ui/test_runner.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(229,26): error C2530: 'rec': references must be initialized
  (compiling source file 'src/ui/test_runner.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(229,26): error C3531: 'rec': a symbol whose type contains 'auto' must have an initializer
  (compiling source file 'src/ui/test_runner.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(229,30): error C2143: syntax error: missing ';' before ':'
  (compiling source file 'src/ui/test_runner.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(229,47): error C2143: syntax error: missing ';' before ')'
  (compiling source file 'src/ui/test_runner.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(238,17): error C2039: 'SetRecommendations': is not a member of 'ui::SecurityStatusSummary'
  (compiling source file 'src/ui/test_runner.cpp')
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\components.hpp(192,11):
      see declaration of 'ui::SecurityStatusSummary'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(241,9): error C2065: 'SecurityRecommendation': undeclared identifier
  (compiling source file 'src/ui/test_runner.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(241,32): error C2146: syntax error: missing ';' before identifier 'customRec'
  (compiling source file 'src/ui/test_runner.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(241,32): error C3861: 'customRec': identifier not found
  (compiling source file 'src/ui/test_runner.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(247,17): error C2039: 'AddRecommendation': is not a member of 'ui::SecurityStatusSummary'
  (compiling source file 'src/ui/test_runner.cpp')
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\components.hpp(192,11):
      see declaration of 'ui::SecurityStatusSummary'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(247,35): error C2065: 'customRec': undeclared identifier
  (compiling source file 'src/ui/test_runner.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(285,37): error C2039: 'GenerateRecommendations': is not a member of 'ui::SecurityStatusSummary'
  (compiling source file 'src/ui/test_runner.cpp')
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\components.hpp(192,11):
      see declaration of 'ui::SecurityStatusSummary'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(286,9): error C3536: 'criticalRecs': cannot be used before it is initialized
  (compiling source file 'src/ui/test_runner.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(298,36): error C2039: 'GenerateRecommendations': is not a member of 'ui::SecurityStatusSummary'
  (compiling source file 'src/ui/test_runner.cpp')
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\components.hpp(192,11):
      see declaration of 'ui::SecurityStatusSummary'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(299,9): error C3536: 'warningRecs': cannot be used before it is initialized
  (compiling source file 'src/ui/test_runner.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(307,36): error C2039: 'GenerateRecommendations': is not a member of 'ui::SecurityStatusSummary'
  (compiling source file 'src/ui/test_runner.cpp')
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\components.hpp(192,11):
      see declaration of 'ui::SecurityStatusSummary'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(308,9): error C3536: 'unknownRecs': cannot be used before it is initialized
  (compiling source file 'src/ui/test_runner.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(317,40): error C2039: 'GenerateRecommendations': is not a member of 'ui::SecurityStatusSummary'
  (compiling source file 'src/ui/test_runner.cpp')
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\components.hpp(192,11):
      see declaration of 'ui::SecurityStatusSummary'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(318,9): error C3536: 'lowSecurityRecs': cannot be used before it is initialized
  (compiling source file 'src/ui/test_runner.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_runner.cpp(77,21): error C3861: 'RunAllSecurityDashboardTests': identifier not found
  test_security_dashboard.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(223,40): error C2039: 'GenerateRecommendations': is not a member of 'ui::SecurityStatusSummary'
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\components.hpp(192,11):
      see declaration of 'ui::SecurityStatusSummary'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(225,9): error C3536: 'recommendations': cannot be used before it is initialized
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(229,32): error C3312: no callable 'begin' function found for type 'int'
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(229,32): error C3312: no callable 'end' function found for type 'int'
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(229,26): error C2530: 'rec': references must be initialized
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(229,26): error C3531: 'rec': a symbol whose type contains 'auto' must have an initializer
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(229,30): error C2143: syntax error: missing ';' before ':'
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(229,47): error C2143: syntax error: missing ';' before ')'
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(238,17): error C2039: 'SetRecommendations': is not a member of 'ui::SecurityStatusSummary'
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\components.hpp(192,11):
      see declaration of 'ui::SecurityStatusSummary'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(241,9): error C2065: 'SecurityRecommendation': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(241,32): error C2146: syntax error: missing ';' before identifier 'customRec'
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(241,32): error C3861: 'customRec': identifier not found
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(247,17): error C2039: 'AddRecommendation': is not a member of 'ui::SecurityStatusSummary'
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\components.hpp(192,11):
      see declaration of 'ui::SecurityStatusSummary'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(247,35): error C2065: 'customRec': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(285,37): error C2039: 'GenerateRecommendations': is not a member of 'ui::SecurityStatusSummary'
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\components.hpp(192,11):
      see declaration of 'ui::SecurityStatusSummary'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(286,9): error C3536: 'criticalRecs': cannot be used before it is initialized
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(298,36): error C2039: 'GenerateRecommendations': is not a member of 'ui::SecurityStatusSummary'
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\components.hpp(192,11):
      see declaration of 'ui::SecurityStatusSummary'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(299,9): error C3536: 'warningRecs': cannot be used before it is initialized
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(307,36): error C2039: 'GenerateRecommendations': is not a member of 'ui::SecurityStatusSummary'
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\components.hpp(192,11):
      see declaration of 'ui::SecurityStatusSummary'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(308,9): error C3536: 'unknownRecs': cannot be used before it is initialized
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(317,40): error C2039: 'GenerateRecommendations': is not a member of 'ui::SecurityStatusSummary'
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\components.hpp(192,11):
      see declaration of 'ui::SecurityStatusSummary'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(318,9): error C3536: 'lowSecurityRecs': cannot be used before it is initialized
  test_security_event_handling.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.hpp(137,41): error C2653: 'SecurityActionTrigger': is not a class or namespace name
  (compiling source file 'src/ui/test_security_event_handling.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.hpp(137,35): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
  (compiling source file 'src/ui/test_security_event_handling.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.hpp(137,77): error C2143: syntax error: missing ',' before '&'
  (compiling source file 'src/ui/test_security_event_handling.cpp')
  
  test_security_ui_integration.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.hpp(137,41): error C2653: 'SecurityActionTrigger': is not a class or namespace name
  (compiling source file 'src/ui/test_security_ui_integration.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.hpp(137,35): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
  (compiling source file 'src/ui/test_security_ui_integration.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.hpp(137,77): error C2143: syntax error: missing ',' before '&'
  (compiling source file 'src/ui/test_security_ui_integration.cpp')
  
  test_window_management_enhanced.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_window_management_enhanced.cpp(177,64): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_window_management_enhanced.cpp(177,49): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_window_management_enhanced.cpp(190,60): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_window_management_enhanced.cpp(190,45): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  ui.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.hpp(137,41): error C2653: 'SecurityActionTrigger': is not a class or namespace name
  (compiling source file 'src/ui/ui.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.hpp(137,35): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
  (compiling source file 'src/ui/ui.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.hpp(137,77): error C2143: syntax error: missing ',' before '&'
  (compiling source file 'src/ui/ui.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\ui.cpp(228,33): error C2065: 'ImGuiKey_F3': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\ui.cpp(813,24): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  Main.cpp
