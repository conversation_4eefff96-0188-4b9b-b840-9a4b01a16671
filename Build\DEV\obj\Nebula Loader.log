﻿  PORTABLE SECURITY BUILD SYSTEM
  ==============================
  Generating new BUILD_SIGNATURE_LEGACY: 0x2F211F9B
  Note: Portable system uses runtime entropy, this is just for fallback
  Successfully updated BUILD_SIGNATURE_LEGACY to: 0x2F211F9B
  
  Build signature update completed.
  Main.cpp
  config.cpp
  core.cpp
  debug.cpp
  security.cpp
  ui.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\ui.cpp(418,34): error C2065: 'window_pos': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\ui.cpp(418,48): error C2065: 'window_pos': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\ui.cpp(719,10): error C2039: 'init': is not a member of 'ui'
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\ui.hpp(5,11):
      see declaration of 'ui'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\ui.cpp(720,3): error C2065: 'dev': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\ui.cpp(721,7): error C2065: 'window_pos': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\ui.cpp(724,5): error C2065: 'screen_res': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\ui.cpp(725,5): error C2065: 'window_pos': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\ui.cpp(725,19): error C2065: 'screen_res': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\ui.cpp(725,32): error C2065: 'window_size': undeclared identifier
