﻿  PORTABLE SECURITY BUILD SYSTEM
  ==============================
  Generating new BUILD_SIGNATURE_LEGACY: 0xF4C951FF
  Note: Portable system uses runtime entropy, this is just for fallback
  Successfully updated BUILD_SIGNATURE_LEGACY to: 0xF4C951FF
  
  Build signature update completed.
  debug.cpp
  config.cpp
  security.cpp
  core.cpp
  accessibility.cpp
  components.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\components.cpp(27,14): error C1075: '{': no matching token found
  performance_monitor.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\performance_monitor.cpp(103,28): warning C4244: 'initializing': conversion from 'ULONGLONG' to 'double', possible loss of data
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\performance_monitor.cpp(169,34): error C2589: '(': illegal token on right side of '::'
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\performance_monitor.cpp(169,34): error C2059: syntax error: ')'
  security_integration.cpp
  ui.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\ui.cpp(234,33): error C2065: 'ImGuiKey_F3': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\ui.cpp(819,24): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  Main.cpp
