﻿  PORTABLE SECURITY BUILD SYSTEM
  ==============================
  Generating new BUILD_SIGNATURE_LEGACY: 0x89B0890D
  Note: Portable system uses runtime entropy, this is just for fallback
  Successfully updated BUILD_SIGNATURE_LEGACY to: 0x89B0890D
  
  Build signature update completed.
  debug.cpp
  config.cpp
  security.cpp
  core.cpp
  accessibility.cpp
  animation.cpp
  components.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.hpp(137,41): error C2653: 'SecurityActionTrigger': is not a class or namespace name
  (compiling source file 'src/ui/components.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.hpp(137,35): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
  (compiling source file 'src/ui/components.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.hpp(137,77): error C2143: syntax error: missing ',' before '&'
  (compiling source file 'src/ui/components.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\components.cpp(27,14): error C1075: '{': no matching token found
  performance_monitor.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\performance_monitor.cpp(103,28): warning C4244: 'initializing': conversion from 'ULONGLONG' to 'double', possible loss of data
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\performance_monitor.cpp(169,34): error C2589: '(': illegal token on right side of '::'
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\performance_monitor.cpp(169,34): error C2059: syntax error: ')'
  security_integration.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.hpp(137,41): error C2653: 'SecurityActionTrigger': is not a class or namespace name
  (compiling source file 'src/ui/security_integration.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.hpp(137,35): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
  (compiling source file 'src/ui/security_integration.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.hpp(137,77): error C2143: syntax error: missing ',' before '&'
  (compiling source file 'src/ui/security_integration.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.cpp(260,17): error C2664: 'void ui::SecurityIntegration::HandleSecurityAction(const int)': cannot convert argument 1 from 'const ui::SecurityActionTrigger::ActionRequest' to 'const int'
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.cpp(260,38):
      No user-defined-conversion operator available that can perform this conversion, or the operator cannot be called
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.hpp(137,14):
      see declaration of 'ui::SecurityIntegration::HandleSecurityAction'
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.cpp(260,17):
      while trying to match the argument list '(const ui::SecurityActionTrigger::ActionRequest)'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.cpp(570,31): error C2511: 'void ui::SecurityIntegration::HandleSecurityAction(const ui::SecurityActionTrigger::ActionRequest &)': overloaded member function not found in 'ui::SecurityIntegration'
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.hpp(71,11):
      see declaration of 'ui::SecurityIntegration'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.cpp(577,25): error C2352: 'ui::SecurityIntegration::RunAllSecurityChecks': a call of a non-static member function requires an object
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.cpp(231,31):
      see declaration of 'ui::SecurityIntegration::RunAllSecurityChecks'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.cpp(588,25): error C2352: 'ui::SecurityIntegration::RunSecurityCheck': a call of a non-static member function requires an object
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.cpp(166,31):
      see declaration of 'ui::SecurityIntegration::RunSecurityCheck'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.cpp(593,21): error C2352: 'ui::SecurityIntegration::StopRealTimeMonitoring': a call of a non-static member function requires an object
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.cpp(270,31):
      see declaration of 'ui::SecurityIntegration::StopRealTimeMonitoring'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.cpp(594,21): error C2352: 'ui::SecurityIntegration::StartRealTimeMonitoring': a call of a non-static member function requires an object
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.cpp(244,31):
      see declaration of 'ui::SecurityIntegration::StartRealTimeMonitoring'
  
  test_performance_accessibility.cpp
  test_runner.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.hpp(137,41): error C2653: 'SecurityActionTrigger': is not a class or namespace name
  (compiling source file 'src/ui/test_runner.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.hpp(137,35): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
  (compiling source file 'src/ui/test_runner.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.hpp(137,77): error C2143: syntax error: missing ',' before '&'
  (compiling source file 'src/ui/test_runner.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(223,40): error C2039: 'GenerateRecommendations': is not a member of 'ui::SecurityStatusSummary'
  (compiling source file 'src/ui/test_runner.cpp')
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\components.hpp(192,11):
      see declaration of 'ui::SecurityStatusSummary'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(225,9): error C3536: 'recommendations': cannot be used before it is initialized
  (compiling source file 'src/ui/test_runner.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(229,32): error C3312: no callable 'begin' function found for type 'int'
  (compiling source file 'src/ui/test_runner.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(229,32): error C3312: no callable 'end' function found for type 'int'
  (compiling source file 'src/ui/test_runner.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(229,26): error C2530: 'rec': references must be initialized
  (compiling source file 'src/ui/test_runner.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(229,26): error C3531: 'rec': a symbol whose type contains 'auto' must have an initializer
  (compiling source file 'src/ui/test_runner.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(229,30): error C2143: syntax error: missing ';' before ':'
  (compiling source file 'src/ui/test_runner.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(229,47): error C2143: syntax error: missing ';' before ')'
  (compiling source file 'src/ui/test_runner.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(238,17): error C2039: 'SetRecommendations': is not a member of 'ui::SecurityStatusSummary'
  (compiling source file 'src/ui/test_runner.cpp')
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\components.hpp(192,11):
      see declaration of 'ui::SecurityStatusSummary'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(241,9): error C2065: 'SecurityRecommendation': undeclared identifier
  (compiling source file 'src/ui/test_runner.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(241,32): error C2146: syntax error: missing ';' before identifier 'customRec'
  (compiling source file 'src/ui/test_runner.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(241,32): error C3861: 'customRec': identifier not found
  (compiling source file 'src/ui/test_runner.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(247,17): error C2039: 'AddRecommendation': is not a member of 'ui::SecurityStatusSummary'
  (compiling source file 'src/ui/test_runner.cpp')
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\components.hpp(192,11):
      see declaration of 'ui::SecurityStatusSummary'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(247,35): error C2065: 'customRec': undeclared identifier
  (compiling source file 'src/ui/test_runner.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(285,37): error C2039: 'GenerateRecommendations': is not a member of 'ui::SecurityStatusSummary'
  (compiling source file 'src/ui/test_runner.cpp')
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\components.hpp(192,11):
      see declaration of 'ui::SecurityStatusSummary'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(286,9): error C3536: 'criticalRecs': cannot be used before it is initialized
  (compiling source file 'src/ui/test_runner.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(298,36): error C2039: 'GenerateRecommendations': is not a member of 'ui::SecurityStatusSummary'
  (compiling source file 'src/ui/test_runner.cpp')
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\components.hpp(192,11):
      see declaration of 'ui::SecurityStatusSummary'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(299,9): error C3536: 'warningRecs': cannot be used before it is initialized
  (compiling source file 'src/ui/test_runner.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(307,36): error C2039: 'GenerateRecommendations': is not a member of 'ui::SecurityStatusSummary'
  (compiling source file 'src/ui/test_runner.cpp')
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\components.hpp(192,11):
      see declaration of 'ui::SecurityStatusSummary'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(308,9): error C3536: 'unknownRecs': cannot be used before it is initialized
  (compiling source file 'src/ui/test_runner.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(317,40): error C2039: 'GenerateRecommendations': is not a member of 'ui::SecurityStatusSummary'
  (compiling source file 'src/ui/test_runner.cpp')
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\components.hpp(192,11):
      see declaration of 'ui::SecurityStatusSummary'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(318,9): error C3536: 'lowSecurityRecs': cannot be used before it is initialized
  (compiling source file 'src/ui/test_runner.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_runner.cpp(77,21): error C3861: 'RunAllSecurityDashboardTests': identifier not found
  test_security_dashboard.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(223,40): error C2039: 'GenerateRecommendations': is not a member of 'ui::SecurityStatusSummary'
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\components.hpp(192,11):
      see declaration of 'ui::SecurityStatusSummary'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(225,9): error C3536: 'recommendations': cannot be used before it is initialized
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(229,32): error C3312: no callable 'begin' function found for type 'int'
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(229,32): error C3312: no callable 'end' function found for type 'int'
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(229,26): error C2530: 'rec': references must be initialized
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(229,26): error C3531: 'rec': a symbol whose type contains 'auto' must have an initializer
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(229,30): error C2143: syntax error: missing ';' before ':'
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(229,47): error C2143: syntax error: missing ';' before ')'
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(238,17): error C2039: 'SetRecommendations': is not a member of 'ui::SecurityStatusSummary'
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\components.hpp(192,11):
      see declaration of 'ui::SecurityStatusSummary'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(241,9): error C2065: 'SecurityRecommendation': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(241,32): error C2146: syntax error: missing ';' before identifier 'customRec'
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(241,32): error C3861: 'customRec': identifier not found
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(247,17): error C2039: 'AddRecommendation': is not a member of 'ui::SecurityStatusSummary'
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\components.hpp(192,11):
      see declaration of 'ui::SecurityStatusSummary'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(247,35): error C2065: 'customRec': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(285,37): error C2039: 'GenerateRecommendations': is not a member of 'ui::SecurityStatusSummary'
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\components.hpp(192,11):
      see declaration of 'ui::SecurityStatusSummary'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(286,9): error C3536: 'criticalRecs': cannot be used before it is initialized
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(298,36): error C2039: 'GenerateRecommendations': is not a member of 'ui::SecurityStatusSummary'
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\components.hpp(192,11):
      see declaration of 'ui::SecurityStatusSummary'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(299,9): error C3536: 'warningRecs': cannot be used before it is initialized
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(307,36): error C2039: 'GenerateRecommendations': is not a member of 'ui::SecurityStatusSummary'
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\components.hpp(192,11):
      see declaration of 'ui::SecurityStatusSummary'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(308,9): error C3536: 'unknownRecs': cannot be used before it is initialized
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(317,40): error C2039: 'GenerateRecommendations': is not a member of 'ui::SecurityStatusSummary'
      C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\components.hpp(192,11):
      see declaration of 'ui::SecurityStatusSummary'
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_security_dashboard.cpp(318,9): error C3536: 'lowSecurityRecs': cannot be used before it is initialized
  test_security_event_handling.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.hpp(137,41): error C2653: 'SecurityActionTrigger': is not a class or namespace name
  (compiling source file 'src/ui/test_security_event_handling.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.hpp(137,35): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
  (compiling source file 'src/ui/test_security_event_handling.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.hpp(137,77): error C2143: syntax error: missing ',' before '&'
  (compiling source file 'src/ui/test_security_event_handling.cpp')
  
  test_security_ui_integration.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.hpp(137,41): error C2653: 'SecurityActionTrigger': is not a class or namespace name
  (compiling source file 'src/ui/test_security_ui_integration.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.hpp(137,35): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
  (compiling source file 'src/ui/test_security_ui_integration.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.hpp(137,77): error C2143: syntax error: missing ',' before '&'
  (compiling source file 'src/ui/test_security_ui_integration.cpp')
  
  test_window_management_enhanced.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_window_management_enhanced.cpp(177,64): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_window_management_enhanced.cpp(177,49): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_window_management_enhanced.cpp(190,60): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\test_window_management_enhanced.cpp(190,45): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  ui.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.hpp(137,41): error C2653: 'SecurityActionTrigger': is not a class or namespace name
  (compiling source file 'src/ui/ui.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.hpp(137,35): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
  (compiling source file 'src/ui/ui.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\security_integration.hpp(137,77): error C2143: syntax error: missing ',' before '&'
  (compiling source file 'src/ui/ui.cpp')
  
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\ui.cpp(234,33): error C2065: 'ImGuiKey_F3': undeclared identifier
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\src\ui\ui.cpp(819,24): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  Main.cpp
