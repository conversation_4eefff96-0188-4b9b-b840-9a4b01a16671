<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="DEV|Win32">
      <Configuration>DEV</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="DEV|x64">
      <Configuration>DEV</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>16.0</VCProjectVersion>
    <ProjectGuid>{82212CF7-C4EA-436C-ADD5-F7AD56122BF5}</ProjectGuid>
    <RootNamespace>Nebula Loader</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
    <ProjectName>Nebula Loader</ProjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='DEV|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='DEV|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='DEV|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='DEV|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <OutDir>$(ProjectDir)Build\Debug\</OutDir>
    <IntDir>$(ProjectDir)Build\Debug\obj\</IntDir>
    <IncludePath>$(ProjectDir)external\imgui;$(IncludePath)</IncludePath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <OutDir>$(ProjectDir)Build\Release\</OutDir>
    <IntDir>$(ProjectDir)Build\Release\obj\</IntDir>
    <IncludePath>$(ProjectDir)external\imgui;$(IncludePath)</IncludePath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='DEV|Win32'">
    <OutDir>$(ProjectDir)Build\DEV\</OutDir>
    <IntDir>$(ProjectDir)Build\DEV\obj\</IntDir>
    <IncludePath>$(ProjectDir)external\imgui;$(IncludePath)</IncludePath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <OutDir>$(ProjectDir)Build\Debug\</OutDir>
    <IntDir>$(ProjectDir)Build\Debug\obj\</IntDir>
    <IncludePath>$(ProjectDir)external\imgui;$(IncludePath)</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>$(ProjectDir)Build\Release\</OutDir>
    <IntDir>$(ProjectDir)Build\Release\obj\</IntDir>
    <IncludePath>$(ProjectDir)external\imgui;$(IncludePath)</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='DEV|x64'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>$(ProjectDir)Build\DEV\</OutDir>
    <IntDir>$(ProjectDir)Build\DEV\obj\</IntDir>
    <IncludePath>$(ProjectDir)external\imgui;$(IncludePath)</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <SDLCheck>true</SDLCheck>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <LanguageStandard_C>stdc17</LanguageStandard_C>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
    </Link>
    <PreBuildEvent>
      <Command>cd /d "$(ProjectDir)" &amp;&amp; powershell -ExecutionPolicy Bypass -File "update_signature.ps1"</Command>
    </PreBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <SDLCheck>true</SDLCheck>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <LanguageStandard_C>stdc17</LanguageStandard_C>
      <PreprocessorDefinitions />
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <AdditionalDependencies>external\lib\library_x64.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
    <PreBuildEvent>
      <Command>cd /d "$(ProjectDir)" &amp;&amp; powershell -ExecutionPolicy Bypass -File "update_signature.ps1"</Command>
    </PreBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <LanguageStandard_C>stdc17</LanguageStandard_C>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
    <PreBuildEvent>
      <Command>cd /d "$(ProjectDir)" &amp;&amp; powershell -ExecutionPolicy Bypass -File "update_signature.ps1"</Command>
    </PreBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='DEV|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <LanguageStandard_C>stdc17</LanguageStandard_C>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
    <PreBuildEvent>
      <Command>cd /d "$(ProjectDir)" &amp;&amp; powershell -ExecutionPolicy Bypass -File "update_signature.ps1"</Command>
    </PreBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <LanguageStandard_C>stdc17</LanguageStandard_C>
      <PreprocessorDefinitions>
      </PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalDependencies>external\lib\library_x64.lib;iphlpapi.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
    <PreBuildEvent>
      <Command>cd /d "$(ProjectDir)" &amp;&amp; powershell -ExecutionPolicy Bypass -File "update_signature.ps1"</Command>
    </PreBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='DEV|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <LanguageStandard_C>stdc17</LanguageStandard_C>
      <PreprocessorDefinitions>DEV;DISABLE_SECURITY_INTEGRATION;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalOptions>/FS %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalDependencies>external\lib\library_x64.lib;iphlpapi.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
    <PreBuildEvent>
      <Command>cd /d "$(ProjectDir)" &amp;&amp; powershell -ExecutionPolicy Bypass -File "update_signature.ps1"</Command>
    </PreBuildEvent>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="src\security\debug.cpp" />
    <ClCompile Include="src\security\config.cpp" />
    <ClCompile Include="src\security\security.cpp" />
    <ClCompile Include="src\security\core.cpp" />
    <ClCompile Include="src\security\indirect_crash.cpp" />
    <ClCompile Include="src\ui\accessibility.cpp" />
    <ClCompile Include="src\ui\animation.cpp" />
    <ClCompile Include="src\ui\components.cpp" />
    <ClCompile Include="src\ui\performance_monitor.cpp" />
    <ClCompile Include="src\ui\security_integration.cpp" />
    <ClCompile Include="src\ui\security_stubs.cpp" />
    <ClCompile Include="src\ui\test_window_management.cpp" />
    <ClCompile Include="src\ui\test_window_management_enhanced.cpp" />
    <ClCompile Include="src\ui\theme.cpp" />
    <ClCompile Include="src\ui\ui.cpp" />
    <ClCompile Include="external\imgui\imgui.cpp" />
    <ClCompile Include="external\imgui\imgui_demo.cpp" />
    <ClCompile Include="external\imgui\imgui_draw.cpp" />
    <ClCompile Include="external\imgui\imgui_impl_dx9.cpp" />
    <ClCompile Include="external\imgui\imgui_impl_win32.cpp" />
    <ClCompile Include="external\imgui\imgui_tables.cpp" />
    <ClCompile Include="external\imgui\imgui_widgets.cpp" />
    <ClCompile Include="src\Main.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="src\auth\auth.hpp" />
    <ClInclude Include="src\auth\json.hpp" />
    <ClInclude Include="src\auth\secure_auth.hpp" />
    <ClInclude Include="src\auth\skStr.h" />
    <ClInclude Include="src\auth\utils.hpp" />
    <ClInclude Include="src\globals.hpp" />
    <ClInclude Include="src\security\obfuscation.hpp" />
    <ClInclude Include="src\security\protection.hpp" />
    <ClInclude Include="src\security\debug.hpp" />
    <ClInclude Include="src\security\config.hpp" />
    <ClInclude Include="src\security\encryption.hpp" />
    <ClInclude Include="src\security\integrity.hpp" />
    <ClInclude Include="src\security\security.hpp" />
    <ClInclude Include="src\security\settings.h" />
    <ClInclude Include="src\security\core.hpp" />
    <ClInclude Include="src\security\indirect_crash.hpp" />
    <ClInclude Include="src\security\macros.hpp" />
    <ClInclude Include="src\security\strings.hpp" />
    <ClInclude Include="src\ui\accessibility.hpp" />
    <ClInclude Include="src\ui\animation.hpp" />
    <ClInclude Include="src\ui\components.hpp" />
    <ClInclude Include="src\ui\performance_monitor.hpp" />
    <ClInclude Include="src\ui\security_integration.hpp" />
    <ClInclude Include="src\ui\test_integration.hpp" />
    <ClInclude Include="src\ui\theme.hpp" />
    <ClInclude Include="src\ui\ui.hpp" />
    <ClInclude Include="external\imgui\imconfig.h" />
    <ClInclude Include="external\imgui\imgui.h" />
    <ClInclude Include="external\imgui\imgui_impl_dx9.h" />
    <ClInclude Include="external\imgui\imgui_impl_win32.h" />
    <ClInclude Include="external\imgui\imgui_internal.h" />
    <ClInclude Include="external\imgui\imstb_rectpack.h" />
    <ClInclude Include="external\imgui\imstb_textedit.h" />
    <ClInclude Include="external\imgui\imstb_truetype.h" />
    <ClInclude Include="src\Main.hpp" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>