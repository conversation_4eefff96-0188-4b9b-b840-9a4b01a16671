#pragma once
#include <windows.h>  // For MessageBoxA
#include <tlhelp32.h>  // For process enumeration
#include <string>      // For std::string
#include <algorithm>   // For std::transform
#include <cstdint>     // For uint32_t
#include <cstdlib>     // For rand()
#include "../auth/skStr.h"  // For skCrypt

// ADVANCED SECURITY CONFIGURATION
// Professional anti-cracking and reverse engineering protection

// DYNAMIC CONFIGURATION SYSTEM
// Security settings are now calculated at runtime based on system characteristics
// This makes it much harder for crackers to simply patch compile-time flags

// Base security multiplier (higher = more secure, but may affect performance)
#define SECURITY_INTENSITY_LEVEL 3

// Feature toggles (these are now processed dynamically)
#define ENABLE_ADVANCED_PROTECTION 1
#define ENABLE_STEALTH_OPERATIONS 1
#define ENABLE_DETAILED_DIAGNOSTICS 1

// PORTABLE BUILD SIGNATURE (ersetzt hardcoded Werte)
// Wird zur Laufzeit durch portable Entropie ersetzt
#define BUILD_SIGNATURE_LEGACY 0xE9615B90  // Fallback für Kompatibilität
#define USE_PORTABLE_KEYS 1  // Aktiviert portable Schlüssel-System

// POPUP CONTROL: Set to true to disable specific popup categories
#define DISABLE_SECURITY_POPUPS true    // Security checks and warnings
#define DISABLE_GUI_POPUPS true         // GUI setup and initialization messages
#define DISABLE_ERROR_POPUPS false      // Critical error messages (keep enabled for debugging)
#define DISABLE_INFO_POPUPS true        // General information messages
#define DISABLE_RUNTIME_SECURITY_MESSAGES true  // Runtime security detection messages

// Advanced portable encryption system (works on any PC)
namespace {
  // Multiple entropy sources for strong encryption
  constexpr uint64_t ENTROPY_SEED_1 = 0x8F4A2B1C9E7D3F6A;
  constexpr uint64_t ENTROPY_SEED_2 = 0x5C8E9A2F4B7D1E3C;
  constexpr uint64_t ENTROPY_SEED_3 = 0x9B8E7A5D4C3F2E1A;
  constexpr uint64_t ENTROPY_SEED_4 = 0x3C6EF372FE94F82B;
  constexpr uint32_t CHECKSUM_BASE = 0x7F4E2D1C;

  // Additional security constants for obfuscation
  constexpr uint64_t VALIDATION_SEED = 0x8F4A2B1C9E7D3F6A;
  constexpr uint32_t CHECKSUM_MASK = 0xA5C3E7B9;

  // Portable key generation (no hardware dependency)
  class PortableKeyGenerator {
  private:
    // Generate pseudo-random entropy from portable signature and runtime
    static uint64_t GenerateRuntimeEntropy() {
      uint64_t entropy = BUILD_SIGNATURE_LEGACY;

      // Mix with current time (but in a deterministic way for decryption)
      SYSTEMTIME st;
      GetSystemTime(&st);
      entropy ^= (st.wYear * 0x9E3779B9ULL);
      entropy ^= (st.wMonth * 0x85EBCA6BULL);
      entropy ^= (st.wDay * 0x6A09E667ULL);

      // Add process-specific but deterministic entropy
      entropy ^= GetCurrentProcessId() * 0xBB67AE85ULL;

      return entropy;
    }

  public:
    // Generate 256-bit key array for encryption
    static void Generate256BitKey(uint64_t key_array[4], uint32_t seed = 0) {
      uint64_t base_entropy = GenerateRuntimeEntropy();

      // Generate 4x 64-bit keys (256-bit total)
      key_array[0] = base_entropy ^ ENTROPY_SEED_1 ^ (seed * 0x9E3779B9ULL);
      key_array[1] = (base_entropy << 13) ^ ENTROPY_SEED_2 ^ (seed * 0x85EBCA6BULL);
      key_array[2] = (base_entropy >> 7) ^ ENTROPY_SEED_3 ^ (seed * 0x6A09E667ULL);
      key_array[3] = (base_entropy * 0x85EBCA6B) ^ ENTROPY_SEED_4 ^ (seed * 0xBB67AE85ULL);
    }

    // PORTABLE SESSION-SPECIFIC KEY
    static uint32_t GenerateSessionKey() {
      uint64_t base = GenerateRuntimeEntropy();
      uint32_t session = (uint32_t)(base ^ CHECKSUM_BASE);

      // Add portable runtime variation
      session ^= GetTickCount() & 0xFFFFF000; // Mask to make it more stable
      session ^= GetCurrentProcessId() * 0xC2B2AE35;

      // Add portable entropy from performance counter
      LARGE_INTEGER counter;
      QueryPerformanceCounter(&counter);
      session ^= (uint32_t)(counter.QuadPart & 0xFFFFFFFF);

      return session;
    }

    // PORTABLE CONTEXT-AWARE KEY GENERATION
    static uint32_t GenerateSimpleKey(const char* context = nullptr) {
#if USE_PORTABLE_KEYS
      // Use portable key system instead of hardcoded BUILD_SIGNATURE
      uint64_t portable_key = 0;

      // Fallback wenn portable system nicht verfügbar
      try {
        // Forward declaration - wird in portable_security.hpp definiert
        extern uint64_t GetPortableContextKey(const char* context);
        portable_key = GetPortableContextKey(context);
      } catch (...) {
        portable_key = BUILD_SIGNATURE_LEGACY; // Fallback
      }

      uint32_t key = (uint32_t)(portable_key ^ CHECKSUM_BASE);
#else
      uint32_t key = BUILD_SIGNATURE_LEGACY ^ CHECKSUM_BASE;
#endif

      if (context) {
        // Add context-specific entropy
        for (int i = 0; context[i]; i++) {
          key = (key << 5) + key + context[i];
        }
      }

      return key;
    }
  };

  // Automatic configuration based on build mode
  // Check for any development indicators
#if defined(DEV)
  // Development mode - bypass security for debugging
  constexpr uint64_t DEV_BYPASS_KEY = 0x1A2B3C4D5E6F7890;
#else
  // Release mode - full security protection
  constexpr uint64_t DEV_BYPASS_KEY = 0x8F4A2B1C9E7D3F6A;
#endif
}

namespace SecurityConfig {
    // Forward declaration for runtime config
    class DynamicConfig {
    private:
        static std::uint32_t s_runtime_seed;
        static bool s_config_initialized;

        // PORTABLE RUNTIME CONFIGURATION
        static std::uint32_t CalculateRuntimeSeed() {
#if USE_PORTABLE_KEYS
            // Use portable key system
            std::uint32_t seed = 0;
            try {
                extern uint64_t GetPortableContextKey(const char* context);
                seed = (uint32_t)GetPortableContextKey(skCrypt("runtime_config").decrypt());
            } catch (...) {
                seed = BUILD_SIGNATURE_LEGACY; // Fallback
            }
#else
            std::uint32_t seed = BUILD_SIGNATURE_LEGACY;
#endif

            // Mix in portable system characteristics
            SYSTEM_INFO si;
            GetSystemInfo(&si);
            seed ^= si.dwNumberOfProcessors * 0x9E3779B9;
            seed ^= si.dwPageSize * 0x85EBCA6B;

            // Add portable timing component
            seed ^= GetTickCount() * 0x6A09E667;
            seed ^= GetCurrentProcessId() * 0xBB67AE85;

            // Add performance counter for additional entropy
            LARGE_INTEGER counter;
            QueryPerformanceCounter(&counter);
            seed ^= (uint32_t)(counter.QuadPart * 0x517CC1B7);

            return seed;
        }

    public:
        static void Initialize() {
            if (!s_config_initialized) {
                s_runtime_seed = CalculateRuntimeSeed();
                s_config_initialized = true;
            }
        }

        // Dynamic security level calculation
        static bool IsFeatureEnabled(std::uint32_t feature_id) {
            Initialize();

            std::uint32_t hash = s_runtime_seed;
            hash ^= feature_id * SECURITY_INTENSITY_LEVEL;
            hash = (hash << 7) | (hash >> 25); // Rotate
            hash ^= 0x9E3779B9;

            // Different features have different activation thresholds
            std::uint32_t threshold = 0x80000000; // Base 50%

            switch (feature_id % 16) {
                case 0: threshold = 0x70000000; break; // Debug mode detection
                case 1: threshold = 0x60000000; break; // VM detection
                case 2: threshold = 0x90000000; break; // Timing checks
                case 3: threshold = 0x75000000; break; // Integrity checks
                case 4: threshold = 0x85000000; break; // Hook detection
                case 5: threshold = 0x65000000; break; // Memory protection
                case 6: threshold = 0x95000000; break; // Hardware BP
                case 7: threshold = 0x55000000; break; // Thread monitoring
                case 8: threshold = 0x88000000; break; // Stealth mode
                case 9: threshold = 0x72000000; break; // Error messages
                case 10: threshold = 0x68000000; break; // Security popups
                case 11: threshold = 0x92000000; break; // String encryption
                case 12: threshold = 0x78000000; break; // Control flow
                case 13: threshold = 0x82000000; break; // Anti-analysis
                case 14: threshold = 0x58000000; break; // Sandbox detection
                case 15: threshold = 0x98000000; break; // Process monitoring
            }

            return hash > threshold;
        }

        // Check if we're in a development environment
        static bool IsDevelopmentEnvironment() {
          // Simplified development check using preprocessor
          #if defined(DEV)
           return true;  // Development mode
          #else
           return false; // Release mode
          #endif
        }
    };

    // Static member definitions
    // Static member definitions moved to debug_config.cpp to avoid linker errors

    // Dynamic configuration getters - replace old constexpr bools
    inline bool ENABLE_DEBUG_MODE() {
        return DynamicConfig::IsDevelopmentEnvironment();
    }

    inline bool DISABLE_VM_DETECTION() {
        // FIXED: Always enable VM detection unless in development environment
        return DynamicConfig::IsDevelopmentEnvironment();
    }

    inline bool RELAXED_TIMING_CHECKS() {
        // FIXED: Always enable timing checks unless in development environment
        return DynamicConfig::IsDevelopmentEnvironment();
    }

    inline bool DISABLE_INTEGRITY_CHECKS() {
        // FIXED: Always enable integrity checks unless in development environment
        return DynamicConfig::IsDevelopmentEnvironment();
    }

    inline bool REDUCE_CHECK_FREQUENCY() {
        return DynamicConfig::IsDevelopmentEnvironment() || !DynamicConfig::IsFeatureEnabled(0x1010);
    }

    inline bool DISABLE_PARENT_CHECK() {
        return DynamicConfig::IsDevelopmentEnvironment() || !DynamicConfig::IsFeatureEnabled(0x1011);
    }

    inline bool DISABLE_PROCESS_CHECKS() {
        // Only disable in development mode (controlled by hidden compile-time flag)
        return DynamicConfig::IsDevelopmentEnvironment();
    }

    inline bool DISABLE_DEBUGGER_CHECKS() {
        // FIXED: Always enable debugger checks unless in development environment
        return DynamicConfig::IsDevelopmentEnvironment();
    }

    inline bool ENABLE_RUNTIME_CHECKS() {
        // FIXED: Always enable runtime checks unless in development environment
        return !DynamicConfig::IsDevelopmentEnvironment();
    }

    inline bool ENABLE_HARDWARE_BP_DETECTION() {
        // FIXED: Always enable hardware breakpoint detection unless in development environment
        return ENABLE_ADVANCED_PROTECTION && !DynamicConfig::IsDevelopmentEnvironment();
    }

    inline bool ENABLE_MEMORY_PROTECTION() {
        return ENABLE_ADVANCED_PROTECTION && DynamicConfig::IsFeatureEnabled(0x1016);
    }

    inline bool ENABLE_HOOK_DETECTION() {
        return ENABLE_ADVANCED_PROTECTION && DynamicConfig::IsFeatureEnabled(0x1017);
    }

    inline bool ENABLE_THREAD_MONITORING() {
        return ENABLE_ADVANCED_PROTECTION && DynamicConfig::IsFeatureEnabled(0x1018);
    }

    inline bool ENABLE_STEALTH_MODE() {
        return ENABLE_STEALTH_OPERATIONS && DynamicConfig::IsFeatureEnabled(0x1019);
    }

    inline bool SHOW_DETAILED_SECURITY_ERRORS() {
        return ENABLE_DETAILED_DIAGNOSTICS && DynamicConfig::IsFeatureEnabled(0x101A);
    }

    inline bool SECURITY_POPUPS_ENABLED() {
        return !DynamicConfig::IsDevelopmentEnvironment() && DynamicConfig::IsFeatureEnabled(0x101B);
    }

    inline int OBFUSCATION_LEVEL() {
        return ENABLE_ADVANCED_PROTECTION ? (SECURITY_INTENSITY_LEVEL + 1) : 1;
    }

    inline bool DISABLE_SECURITY_CHECKS() {
        return DynamicConfig::IsDevelopmentEnvironment();
    }
}

// Add Obfuscation namespace with MemoryProtection
namespace Obfuscation {
    namespace MemoryProtection {
        inline void ClearSensitiveData(void* data, size_t size) {
            if (data && size > 0) {
                // Secure zero memory
                volatile char* ptr = static_cast<volatile char*>(data);
                for (size_t i = 0; i < size; ++i) {
                    ptr[i] = 0;
                }
                // Additional security: write random data then zero again
                for (size_t i = 0; i < size; ++i) {
                    ptr[i] = static_cast<char>(rand() % 256);
                }
                for (size_t i = 0; i < size; ++i) {
                    ptr[i] = 0;
                }
            }
        }
    }
}

// Macros to conditionally compile different popup categories
#if DISABLE_SECURITY_POPUPS
    #define SECURITY_POPUP(hwnd, message, title, type) ((void)0)  // Security checks and warnings
#else
    #define SECURITY_POPUP(hwnd, message, title, type) MessageBoxA(hwnd, message, title, type)
#endif

#if DISABLE_GUI_POPUPS
    #define GUI_POPUP(hwnd, message, title, type) ((void)0)  // GUI setup and initialization
#else
    #define GUI_POPUP(hwnd, message, title, type) MessageBoxA(hwnd, message, title, type)
#endif

#if DISABLE_ERROR_POPUPS
    #define ERROR_POPUP(hwnd, message, title, type) ((void)0)  // Critical errors
#else
    #define ERROR_POPUP(hwnd, message, title, type) MessageBoxA(hwnd, message, title, type)
#endif

#if DISABLE_INFO_POPUPS
    #define INFO_POPUP(hwnd, message, title, type) ((void)0)  // General information
#else
    #define INFO_POPUP(hwnd, message, title, type) MessageBoxA(hwnd, message, title, type)
#endif

#if DISABLE_RUNTIME_SECURITY_MESSAGES
    #define RUNTIME_SECURITY_MESSAGE(hwnd, message, title, type) ((void)0)  // Runtime security detection
#else
    #define RUNTIME_SECURITY_MESSAGE(hwnd, message, title, type) MessageBoxA(hwnd, message, title, type)
#endif