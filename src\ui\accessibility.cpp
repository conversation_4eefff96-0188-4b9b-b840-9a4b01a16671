#include "accessibility.hpp"
#include "theme.hpp"
#include <algorithm>
#include <cmath>
#include <iostream>
#include <imgui.h>
#include <limits>

namespace ui {

    // KeyboardNavigation implementation
    KeyboardNavigation& KeyboardNavigation::GetInstance() {
        static KeyboardNavigation instance;
        return instance;
    }

    void KeyboardNavigation::RegisterElement(const NavigableElement& element) {
        m_elements[element.id] = element;
    }

    void KeyboardNavigation::UnregisterElement(const std::string& id) {
        m_elements.erase(id);
        if (m_focusedElementId == id) {
            m_focusedElementId.clear();
        }
    }

    void KeyboardNavigation::UpdateElementPosition(const std::string& id, const ImVec2& position, const ImVec2& size) {
        auto it = m_elements.find(id);
        if (it != m_elements.end()) {
            it->second.position = position;
            it->second.size = size;
        }
    }

    void KeyboardNavigation::HandleKeyboardInput() {
        if (!m_navigationEnabled) return;
        
        ImGuiIO& io = ImGui::GetIO();
        
        if (ImGui::IsKeyPressed(ImGuiKey_Tab)) {
            Navigate(io.KeyShift ? NavigationDirection::Previous : NavigationDirection::Next);
        } else if (ImGui::IsKeyPressed(ImGuiKey_UpArrow)) {
            Navigate(NavigationDirection::Up);
        } else if (ImGui::IsKeyPressed(ImGuiKey_DownArrow)) {
            Navigate(NavigationDirection::Down);
        } else if (ImGui::IsKeyPressed(ImGuiKey_LeftArrow)) {
            Navigate(NavigationDirection::Left);
        } else if (ImGui::IsKeyPressed(ImGuiKey_RightArrow)) {
            Navigate(NavigationDirection::Right);
        } else if (ImGui::IsKeyPressed(ImGuiKey_Enter) || ImGui::IsKeyPressed(ImGuiKey_Space)) {
            ActivateCurrentElement();
        }
    }

    void KeyboardNavigation::Navigate(NavigationDirection direction) {
        NavigableElement* nextElement = FindNearestElement(direction);
        if (nextElement) {
            SetFocusedElement(nextElement->id);
        }
    }

    void KeyboardNavigation::ActivateCurrentElement() {
        auto it = m_elements.find(m_focusedElementId);
        if (it != m_elements.end() && it->second.onActivate) {
            it->second.onActivate();
        }
    }

    void KeyboardNavigation::SetFocusedElement(const std::string& id) {
        if (m_focusedElementId == id) return;
        
        // Blur previous element
        if (!m_focusedElementId.empty()) {
            auto prevIt = m_elements.find(m_focusedElementId);
            if (prevIt != m_elements.end() && prevIt->second.onBlur) {
                prevIt->second.onBlur();
            }
        }
        
        m_focusedElementId = id;
        
        // Focus new element
        auto it = m_elements.find(id);
        if (it != m_elements.end() && it->second.onFocus) {
            it->second.onFocus();
        }
        
        // Announce to screen reader
        if (it != m_elements.end()) {
            ScreenReaderSupport::GetInstance().AnnounceElementFocus("element", id);
        }
    }

    std::string KeyboardNavigation::GetFocusedElement() const {
        return m_focusedElementId;
    }

    bool KeyboardNavigation::IsElementFocused(const std::string& id) const {
        return m_focusedElementId == id;
    }

    void KeyboardNavigation::ClearAllElements() {
        m_elements.clear();
        m_focusedElementId.clear();
    }

    void KeyboardNavigation::SetNavigationEnabled(bool enabled) {
        m_navigationEnabled = enabled;
    }

    KeyboardNavigation::NavigableElement* KeyboardNavigation::FindNearestElement(NavigationDirection direction) {
        if (m_elements.empty()) return nullptr;
        
        ImVec2 currentPos(0, 0);
        if (!m_focusedElementId.empty()) {
            auto it = m_elements.find(m_focusedElementId);
            if (it != m_elements.end()) {
                currentPos = it->second.position;
            }
        }
        
        NavigableElement* nearest = nullptr;
        float nearestDistance = std::numeric_limits<float>::max();
        
        for (auto& [id, element] : m_elements) {
            if (id == m_focusedElementId || !element.isVisible || !element.isEnabled) continue;
            
            float distance = CalculateDistance(currentPos, element.position, direction);
            if (distance < nearestDistance) {
                nearestDistance = distance;
                nearest = &element;
            }
        }
        
        return nearest;
    }

    float KeyboardNavigation::CalculateDistance(const ImVec2& from, const ImVec2& to, NavigationDirection direction) {
        ImVec2 diff = ImVec2(to.x - from.x, to.y - from.y);
        
        switch (direction) {
            case NavigationDirection::Up:
                return diff.y < 0 ? std::abs(diff.y) + std::abs(diff.x) * 0.1f : std::numeric_limits<float>::max();
            case NavigationDirection::Down:
                return diff.y > 0 ? std::abs(diff.y) + std::abs(diff.x) * 0.1f : std::numeric_limits<float>::max();
            case NavigationDirection::Left:
                return diff.x < 0 ? std::abs(diff.x) + std::abs(diff.y) * 0.1f : std::numeric_limits<float>::max();
            case NavigationDirection::Right:
                return diff.x > 0 ? std::abs(diff.x) + std::abs(diff.y) * 0.1f : std::numeric_limits<float>::max();
            case NavigationDirection::Next:
            case NavigationDirection::Previous:
                return std::sqrt(diff.x * diff.x + diff.y * diff.y);
        }
        return std::numeric_limits<float>::max();
    }

    // AccessibleTooltip implementation
    AccessibleTooltip& AccessibleTooltip::GetInstance() {
        static AccessibleTooltip instance;
        return instance;
    }

    void AccessibleTooltip::ShowTooltip(const std::string& id, const TooltipInfo& tooltip) {
        if (m_tooltipsEnabled) {
            m_activeTooltips[id] = tooltip;
        }
    }

    void AccessibleTooltip::HideTooltip(const std::string& id) {
        m_activeTooltips.erase(id);
    }

    void AccessibleTooltip::HideAllTooltips() {
        m_activeTooltips.clear();
    }

    void AccessibleTooltip::RenderTooltips() {
        if (!m_tooltipsEnabled) return;
        
        const auto& theme = Theme::GetCurrentTheme();
        
        for (const auto& [id, tooltip] : m_activeTooltips) {
            ImGui::SetNextWindowPos(tooltip.preferredPosition, ImGuiCond_Always);
            
            ImGuiWindowFlags flags = ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoResize |
                                   ImGuiWindowFlags_NoMove | ImGuiWindowFlags_NoScrollbar |
                                   ImGuiWindowFlags_AlwaysAutoResize | ImGuiWindowFlags_NoFocusOnAppearing;
            
            ImGui::PushStyleColor(ImGuiCol_WindowBg, tooltip.isImportant ? theme.warning : theme.background);
            ImGui::PushStyleColor(ImGuiCol_Text, theme.text);
            
            if (ImGui::Begin(("Tooltip_" + id).c_str(), nullptr, flags)) {
                ImGui::Text("%s", tooltip.text.c_str());
                if (!tooltip.detailedDescription.empty()) {
                    ImGui::Separator();
                    ImGui::TextWrapped("%s", tooltip.detailedDescription.c_str());
                }
            }
            ImGui::End();
            
            ImGui::PopStyleColor(2);
        }
    }

    void AccessibleTooltip::SetTooltipsEnabled(bool enabled) {
        m_tooltipsEnabled = enabled;
        if (!enabled) {
            HideAllTooltips();
        }
    }

    // ScreenReaderSupport implementation
    ScreenReaderSupport& ScreenReaderSupport::GetInstance() {
        static ScreenReaderSupport instance;
        return instance;
    }

    void ScreenReaderSupport::Announce(const std::string& text, bool interrupt) {
        if (m_enabled) {
            OutputToScreenReader(text);
        }
    }

    void ScreenReaderSupport::AnnounceElementFocus(const std::string& elementType, const std::string& elementName) {
        if (m_enabled) {
            std::string announcement = elementType + ": " + elementName;
            OutputToScreenReader(announcement);
        }
    }

    void ScreenReaderSupport::AnnounceStateChange(const std::string& element, const std::string& newState) {
        if (m_enabled) {
            std::string announcement = element + " is now " + newState;
            OutputToScreenReader(announcement);
        }
    }

    void ScreenReaderSupport::SetEnabled(bool enabled) {
        m_enabled = enabled;
    }

    bool ScreenReaderSupport::IsEnabled() const {
        return m_enabled;
    }

    void ScreenReaderSupport::OutputToScreenReader(const std::string& text) {
        // Basic implementation - in a real application, this would interface with screen reader APIs
        // For now, we'll output to console for debugging
        std::cout << "[Screen Reader]: " << text << std::endl;
    }

    // AccessibilitySystem implementation
    AccessibilitySystem& AccessibilitySystem::GetInstance() {
        static AccessibilitySystem instance;
        return instance;
    }

    void AccessibilitySystem::Initialize() {
        if (m_initialized) return;

        // Initialize subsystems
        KeyboardNavigation::GetInstance().SetNavigationEnabled(m_config.keyboardNavigationEnabled);
        AccessibleTooltip::GetInstance().SetTooltipsEnabled(m_config.tooltipsEnabled);
        ScreenReaderSupport::GetInstance().SetEnabled(m_config.screenReaderSupport);

        // Apply initial theme
        ApplyAccessibilityTheme();

        m_initialized = true;
    }

    void AccessibilitySystem::Update() {
        if (!m_initialized) return;

        // Handle keyboard navigation
        if (m_config.keyboardNavigationEnabled) {
            KeyboardNavigation::GetInstance().HandleKeyboardInput();
        }

        // Render tooltips
        if (m_config.tooltipsEnabled) {
            AccessibleTooltip::GetInstance().RenderTooltips();
        }
    }

    void AccessibilitySystem::Shutdown() {
        KeyboardNavigation::GetInstance().ClearAllElements();
        AccessibleTooltip::GetInstance().HideAllTooltips();
        m_initialized = false;
    }

    void AccessibilitySystem::SetConfig(const AccessibilityConfig& config) {
        m_config = config;

        if (m_initialized) {
            // Update subsystems
            KeyboardNavigation::GetInstance().SetNavigationEnabled(config.keyboardNavigationEnabled);
            AccessibleTooltip::GetInstance().SetTooltipsEnabled(config.tooltipsEnabled);
            ScreenReaderSupport::GetInstance().SetEnabled(config.screenReaderSupport);

            // Apply theme changes
            ApplyAccessibilityTheme();
        }
    }

    const AccessibilityConfig& AccessibilitySystem::GetConfig() const {
        return m_config;
    }

    void AccessibilitySystem::SetHighContrastMode(bool enabled) {
        m_config.highContrastMode = enabled;
        ApplyAccessibilityTheme();
    }

    void AccessibilitySystem::SetLargeTextMode(bool enabled) {
        m_config.largeTextMode = enabled;
        m_config.textScale = enabled ? 1.25f : 1.0f;
        UpdateImGuiStyle();
    }

    void AccessibilitySystem::SetKeyboardNavigationEnabled(bool enabled) {
        m_config.keyboardNavigationEnabled = enabled;
        KeyboardNavigation::GetInstance().SetNavigationEnabled(enabled);
    }

    void AccessibilitySystem::SetScreenReaderSupport(bool enabled) {
        m_config.screenReaderSupport = enabled;
        ScreenReaderSupport::GetInstance().SetEnabled(enabled);
    }

    void AccessibilitySystem::SetTooltipsEnabled(bool enabled) {
        m_config.tooltipsEnabled = enabled;
        AccessibleTooltip::GetInstance().SetTooltipsEnabled(enabled);
    }

    void AccessibilitySystem::SetAnimationsReduced(bool enabled) {
        m_config.animationsReduced = enabled;
    }

    void AccessibilitySystem::SetTextScale(float scale) {
        m_config.textScale = std::max(0.5f, std::min(scale, 3.0f));
        UpdateImGuiStyle();
    }

    void AccessibilitySystem::SetUIScale(float scale) {
        m_config.uiScale = std::max(0.5f, std::min(scale, 2.0f));
        UpdateImGuiStyle();
    }

    void AccessibilitySystem::SetContrastMultiplier(float multiplier) {
        m_config.contrastMultiplier = std::max(0.5f, std::min(multiplier, 3.0f));
        ApplyAccessibilityTheme();
    }

    void AccessibilitySystem::ApplyAccessibilityTheme() {
        if (m_config.highContrastMode) {
            // Apply high contrast colors to ImGui style
            ImGuiStyle& style = ImGui::GetStyle();

            style.Colors[ImGuiCol_WindowBg] = m_config.highContrastBackground;
            style.Colors[ImGuiCol_Text] = m_config.highContrastText;
            style.Colors[ImGuiCol_Button] = ApplyContrast(m_config.highContrastAccent);
            style.Colors[ImGuiCol_ButtonHovered] = ApplyContrast(m_config.highContrastAccent);
            style.Colors[ImGuiCol_ButtonActive] = ApplyContrast(m_config.highContrastAccent);
        }

        UpdateImGuiStyle();
    }

    ImVec4 AccessibilitySystem::GetAccessibleColor(const ImVec4& originalColor) const {
        if (m_config.highContrastMode) {
            // Return high contrast equivalent
            float luminance = 0.299f * originalColor.x + 0.587f * originalColor.y + 0.114f * originalColor.z;
            return luminance > 0.5f ? m_config.highContrastText : m_config.highContrastBackground;
        }

        return ApplyContrast(originalColor);
    }

    float AccessibilitySystem::GetAccessibleTextSize(float originalSize) const {
        return originalSize * m_config.textScale;
    }

    bool AccessibilitySystem::AccessibleButton(const std::string& id, const std::string& label, const ImVec2& size) {
        RegisterNavigableElement(id);

        bool result = ImGui::Button(label.c_str(), size);

        if (KeyboardNavigation::GetInstance().IsElementFocused(id)) {
            // Draw focus indicator
            ImDrawList* drawList = ImGui::GetWindowDrawList();
            ImVec2 min = ImGui::GetItemRectMin();
            ImVec2 max = ImGui::GetItemRectMax();
            drawList->AddRect(min, max, IM_COL32(255, 255, 0, 255), 0.0f, 0, 2.0f);
        }

        return result;
    }

    bool AccessibilitySystem::AccessibleCheckbox(const std::string& id, const std::string& label, bool* value) {
        RegisterNavigableElement(id);

        bool result = ImGui::Checkbox(label.c_str(), value);

        if (result && m_config.screenReaderSupport) {
            ScreenReaderSupport::GetInstance().AnnounceStateChange(label, *value ? "checked" : "unchecked");
        }

        return result;
    }

    bool AccessibilitySystem::AccessibleSlider(const std::string& id, const std::string& label, float* value, float min, float max) {
        RegisterNavigableElement(id);

        bool result = ImGui::SliderFloat(label.c_str(), value, min, max);

        if (result && m_config.screenReaderSupport) {
            ScreenReaderSupport::GetInstance().AnnounceStateChange(label, std::to_string(*value));
        }

        return result;
    }

    void AccessibilitySystem::AccessibleText(const std::string& text, bool isHeading) {
        if (isHeading) {
            ImGui::PushFont(nullptr); // Would use heading font if available
            ImGui::Text("%s", text.c_str());
            ImGui::PopFont();

            if (m_config.screenReaderSupport) {
                ScreenReaderSupport::GetInstance().Announce("Heading: " + text);
            }
        } else {
            ImGui::Text("%s", text.c_str());
        }
    }

    void AccessibilitySystem::AccessibleSeparator() {
        ImGui::Separator();
        if (m_config.screenReaderSupport) {
            ScreenReaderSupport::GetInstance().Announce("Separator");
        }
    }

    void AccessibilitySystem::ShowTooltip(const std::string& id, const std::string& text, const std::string& detailedDescription) {
        if (m_config.tooltipsEnabled) {
            AccessibleTooltip::TooltipInfo tooltip(text);
            tooltip.detailedDescription = detailedDescription;
            tooltip.preferredPosition = ImGui::GetMousePos();
            AccessibleTooltip::GetInstance().ShowTooltip(id, tooltip);
        }
    }

    void AccessibilitySystem::ShowHelpTooltip(const std::string& helpText) {
        if (ImGui::IsItemHovered()) {
            ShowTooltip("help_tooltip", helpText);
        }
    }

    void AccessibilitySystem::RegisterNavigableElement(const std::string& id, const std::function<void()>& onActivate) {
        ImVec2 pos = ImGui::GetItemRectMin();
        ImVec2 size = ImVec2(ImGui::GetItemRectMax().x - pos.x, ImGui::GetItemRectMax().y - pos.y);

        KeyboardNavigation::NavigableElement element(id, pos, size);
        element.onActivate = onActivate;
        KeyboardNavigation::GetInstance().RegisterElement(element);
    }

    void AccessibilitySystem::UpdateNavigableElement(const std::string& id) {
        ImVec2 pos = ImGui::GetItemRectMin();
        ImVec2 size = ImVec2(ImGui::GetItemRectMax().x - pos.x, ImGui::GetItemRectMax().y - pos.y);
        KeyboardNavigation::GetInstance().UpdateElementPosition(id, pos, size);
    }

    void AccessibilitySystem::AnnounceToScreenReader(const std::string& text, bool interrupt) {
        ScreenReaderSupport::GetInstance().Announce(text, interrupt);
    }

    void AccessibilitySystem::UpdateImGuiStyle() {
        ImGuiStyle& style = ImGui::GetStyle();

        // Apply scaling
        style.ScaleAllSizes(m_config.uiScale);

        // Apply text scaling would be done through font loading
        // This is a simplified approach
    }

    ImVec4 AccessibilitySystem::ApplyContrast(const ImVec4& color) const {
        if (m_config.contrastMultiplier == 1.0f) return color;

        // Simple contrast adjustment
        ImVec4 adjusted = color;
        float factor = m_config.contrastMultiplier;

        adjusted.x = std::max(0.0f, std::min(adjusted.x * factor, 1.0f));
        adjusted.y = std::max(0.0f, std::min(adjusted.y * factor, 1.0f));
        adjusted.z = std::max(0.0f, std::min(adjusted.z * factor, 1.0f));

        return adjusted;
    }

} // namespace ui
