#pragma once

#include <string>
#include <vector>
#include <functional>
#include <map>
#include <imgui.h>

namespace ui {

    // Accessibility configuration
    struct AccessibilityConfig {
        bool highContrastMode = false;
        bool largeTextMode = false;
        bool keyboardNavigationEnabled = true;
        bool screenReaderSupport = false;
        bool tooltipsEnabled = true;
        bool animationsReduced = false;
        
        float textScale = 1.0f;
        float uiScale = 1.0f;
        float contrastMultiplier = 1.0f;
        
        // Color overrides for high contrast mode
        ImVec4 highContrastBackground = ImVec4(0.0f, 0.0f, 0.0f, 1.0f);
        ImVec4 highContrastText = ImVec4(1.0f, 1.0f, 1.0f, 1.0f);
        ImVec4 highContrastAccent = ImVec4(1.0f, 1.0f, 0.0f, 1.0f);
        ImVec4 highContrastDanger = ImVec4(1.0f, 0.0f, 0.0f, 1.0f);
        ImVec4 highContrastSuccess = ImVec4(0.0f, 1.0f, 0.0f, 1.0f);
    };

    // Keyboard navigation system
    class KeyboardNavigation {
    public:
        enum class NavigationDirection {
            Up, Down, Left, Right, Next, Previous
        };
        
        struct NavigableElement {
            std::string id;
            ImVec2 position;
            ImVec2 size;
            bool isVisible = true;
            bool isEnabled = true;
            std::function<void()> onActivate;
            std::function<void()> onFocus;
            std::function<void()> onBlur;

            NavigableElement() = default;
            NavigableElement(const std::string& elementId, const ImVec2& pos, const ImVec2& sz)
                : id(elementId), position(pos), size(sz) {}
        };
        
        static KeyboardNavigation& GetInstance();
        
        void RegisterElement(const NavigableElement& element);
        void UnregisterElement(const std::string& id);
        void UpdateElementPosition(const std::string& id, const ImVec2& position, const ImVec2& size);
        
        void HandleKeyboardInput();
        void Navigate(NavigationDirection direction);
        void ActivateCurrentElement();
        
        void SetFocusedElement(const std::string& id);
        std::string GetFocusedElement() const;
        bool IsElementFocused(const std::string& id) const;
        
        void ClearAllElements();
        void SetNavigationEnabled(bool enabled);
        
    private:
        KeyboardNavigation() = default;
        
        std::map<std::string, NavigableElement> m_elements;
        std::string m_focusedElementId;
        bool m_navigationEnabled = true;
        
        NavigableElement* FindNearestElement(NavigationDirection direction);
        float CalculateDistance(const ImVec2& from, const ImVec2& to, NavigationDirection direction);
    };

    // Tooltip system with accessibility support
    class AccessibleTooltip {
    public:
        struct TooltipInfo {
            std::string text;
            std::string detailedDescription;
            ImVec2 preferredPosition;
            float displayTime = 0.0f; // 0 = show until dismissed
            bool isImportant = false;

            TooltipInfo() = default;
            TooltipInfo(const std::string& t) : text(t) {}
        };
        
        static AccessibleTooltip& GetInstance();
        
        void ShowTooltip(const std::string& id, const TooltipInfo& tooltip);
        void HideTooltip(const std::string& id);
        void HideAllTooltips();
        
        void RenderTooltips();
        void SetTooltipsEnabled(bool enabled);
        
    private:
        AccessibleTooltip() = default;
        
        std::map<std::string, TooltipInfo> m_activeTooltips;
        bool m_tooltipsEnabled = true;
    };

    // Screen reader support (basic implementation)
    class ScreenReaderSupport {
    public:
        static ScreenReaderSupport& GetInstance();
        
        void Announce(const std::string& text, bool interrupt = false);
        void AnnounceElementFocus(const std::string& elementType, const std::string& elementName);
        void AnnounceStateChange(const std::string& element, const std::string& newState);
        
        void SetEnabled(bool enabled);
        bool IsEnabled() const;
        
    private:
        ScreenReaderSupport() = default;
        bool m_enabled = false;
        
        void OutputToScreenReader(const std::string& text);
    };

    // Main accessibility system
    class AccessibilitySystem {
    public:
        static AccessibilitySystem& GetInstance();
        
        void Initialize();
        void Update();
        void Shutdown();
        
        // Configuration
        void SetConfig(const AccessibilityConfig& config);
        const AccessibilityConfig& GetConfig() const;
        
        void SetHighContrastMode(bool enabled);
        void SetLargeTextMode(bool enabled);
        void SetKeyboardNavigationEnabled(bool enabled);
        void SetScreenReaderSupport(bool enabled);
        void SetTooltipsEnabled(bool enabled);
        void SetAnimationsReduced(bool enabled);
        
        void SetTextScale(float scale);
        void SetUIScale(float scale);
        void SetContrastMultiplier(float multiplier);
        
        // Theme integration
        void ApplyAccessibilityTheme();
        ImVec4 GetAccessibleColor(const ImVec4& originalColor) const;
        float GetAccessibleTextSize(float originalSize) const;
        
        // UI helpers
        bool AccessibleButton(const std::string& id, const std::string& label, const ImVec2& size = ImVec2(0, 0));
        bool AccessibleCheckbox(const std::string& id, const std::string& label, bool* value);
        bool AccessibleSlider(const std::string& id, const std::string& label, float* value, float min, float max);
        void AccessibleText(const std::string& text, bool isHeading = false);
        void AccessibleSeparator();
        
        // Tooltip helpers
        void ShowTooltip(const std::string& id, const std::string& text, const std::string& detailedDescription = "");
        void ShowHelpTooltip(const std::string& helpText);
        
        // Keyboard navigation helpers
        void RegisterNavigableElement(const std::string& id, const std::function<void()>& onActivate = nullptr);
        void UpdateNavigableElement(const std::string& id);
        
        // Announcements
        void AnnounceToScreenReader(const std::string& text, bool interrupt = false);
        
        // UI Integration
        void RenderAccessibilitySettings();
        
    private:
        AccessibilitySystem() = default;
        ~AccessibilitySystem() = default;
        AccessibilitySystem(const AccessibilitySystem&) = delete;
        AccessibilitySystem& operator=(const AccessibilitySystem&) = delete;
        
        AccessibilityConfig m_config;
        bool m_initialized = false;
        
        // Helper methods
        void UpdateImGuiStyle();
        ImVec4 ApplyContrast(const ImVec4& color) const;
    };

    // Accessibility utility functions
    namespace AccessibilityUtils {
        // Color utilities
        float CalculateContrast(const ImVec4& color1, const ImVec4& color2);
        bool MeetsContrastRequirement(const ImVec4& foreground, const ImVec4& background, float ratio = 4.5f);
        ImVec4 AdjustColorForContrast(const ImVec4& color, const ImVec4& background, float targetRatio = 4.5f);
        
        // Text utilities
        std::string SanitizeTextForScreenReader(const std::string& text);
        std::string GenerateElementDescription(const std::string& type, const std::string& name, const std::string& state = "");
        
        // Navigation utilities
        bool IsKeyPressed(ImGuiKey key);
        bool IsNavigationKey(ImGuiKey key);
        KeyboardNavigation::NavigationDirection GetNavigationDirection(ImGuiKey key);
    }

} // namespace ui
