// Prevent Windows socket header conflicts
#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif
#ifndef NOMINMAX
#define NOMINMAX
#endif

#include "animation.hpp"
#include "theme.hpp"
#include <algorithm>
#include <cmath>
#include <imgui.h>

// Prevent Windows macro conflicts
#ifdef min
#undef min
#endif
#ifdef max
#undef max
#endif

namespace ui {
namespace Animation {

    // Global animation system instance
    static AnimationSystem g_animationSystem;

    AnimationSystem& GetAnimationSystem() {
        return g_animationSystem;
    }

    // AnimationSystem implementation
    AnimationState& AnimationSystem::CreateAnimation(const std::string& id) {
        return m_animations[id];
    }

    AnimationState* AnimationSystem::GetAnimation(const std::string& id) {
        auto it = m_animations.find(id);
        return (it != m_animations.end()) ? &it->second : nullptr;
    }

    void AnimationSystem::PlayAnimation(const std::string& id) {
        auto* anim = GetAnimation(id);
        if (anim) {
            anim->isPlaying = true;
            anim->elapsed = 0.0f;
            anim->currentValue = anim->startValue;
        }
    }

    void AnimationSystem::PauseAnimation(const std::string& id) {
        auto* anim = GetAnimation(id);
        if (anim) {
            anim->isPlaying = false;
        }
    }

    void AnimationSystem::StopAnimation(const std::string& id) {
        auto* anim = GetAnimation(id);
        if (anim) {
            anim->isPlaying = false;
            anim->elapsed = 0.0f;
            anim->currentValue = anim->startValue;
        }
    }

    void AnimationSystem::ReverseAnimation(const std::string& id) {
        auto* anim = GetAnimation(id);
        if (anim) {
            anim->isReversed = !anim->isReversed;
            std::swap(anim->startValue, anim->endValue);
        }
    }

    void AnimationSystem::Update(float deltaTime) {
        for (auto& [id, anim] : m_animations) {
            if (!anim.isPlaying) continue;

            anim.elapsed += deltaTime;
            float t = std::clamp(anim.elapsed / anim.duration, 0.0f, 1.0f);
            
            // Apply easing
            float easedT = ApplyEasing(t, anim.easing);
            
            // Calculate current value
            anim.currentValue = Lerp(anim.startValue, anim.endValue, easedT);
            
            // Call update callback
            if (anim.onUpdate) {
                anim.onUpdate(anim.currentValue);
            }
            
            // Check if animation is complete
            if (t >= 1.0f) {
                if (anim.loop) {
                    anim.elapsed = 0.0f;
                } else {
                    anim.isPlaying = false;
                    if (anim.onComplete) {
                        anim.onComplete();
                    }
                }
            }
        }
    }

    void AnimationSystem::RemoveAnimation(const std::string& id) {
        m_animations.erase(id);
    }

    void AnimationSystem::ClearAllAnimations() {
        m_animations.clear();
    }

    void AnimationSystem::AnimateFloat(const std::string& id, float& target, float endValue, 
                                     float duration, EasingType easing) {
        AnimateFloatFromTo(id, target, target, endValue, duration, easing);
    }

    void AnimationSystem::AnimateFloatFromTo(const std::string& id, float& target, float startValue, 
                                           float endValue, float duration, EasingType easing) {
        auto& anim = CreateAnimation(id);
        anim.startValue = startValue;
        anim.endValue = endValue;
        anim.duration = duration;
        anim.easing = easing;
        anim.onUpdate = [&target](float value) { target = value; };
        PlayAnimation(id);
    }

    float AnimationSystem::SmoothStep(float value, float target, float speed, float deltaTime) {
        float diff = target - value;
        return value + diff * std::clamp(speed * deltaTime, 0.0f, 1.0f);
    }

    float AnimationSystem::Lerp(float a, float b, float t) {
        return a + (b - a) * t;
    }

    float AnimationSystem::ApplyEasing(float t, EasingType easing) {
        switch (easing) {
            case EasingType::Linear: return Easing::Linear(t);
            case EasingType::EaseIn: return Easing::EaseIn(t);
            case EasingType::EaseOut: return Easing::EaseOut(t);
            case EasingType::EaseInOut: return Easing::EaseInOut(t);
            case EasingType::EaseInQuad: return Easing::EaseInQuad(t);
            case EasingType::EaseOutQuad: return Easing::EaseOutQuad(t);
            case EasingType::EaseInOutQuad: return Easing::EaseInOutQuad(t);
            case EasingType::EaseInCubic: return Easing::EaseInCubic(t);
            case EasingType::EaseOutCubic: return Easing::EaseOutCubic(t);
            case EasingType::EaseInOutCubic: return Easing::EaseInOutCubic(t);
            default: return t;
        }
    }

    // Easing function implementations
    namespace Easing {
        float Linear(float t) {
            return t;
        }

        float EaseIn(float t) {
            return t * t;
        }

        float EaseOut(float t) {
            return 1.0f - (1.0f - t) * (1.0f - t);
        }

        float EaseInOut(float t) {
            return t < 0.5f ? 2.0f * t * t : 1.0f - 2.0f * (1.0f - t) * (1.0f - t);
        }

        float EaseInQuad(float t) {
            return t * t;
        }

        float EaseOutQuad(float t) {
            return 1.0f - (1.0f - t) * (1.0f - t);
        }

        float EaseInOutQuad(float t) {
            return t < 0.5f ? 2.0f * t * t : 1.0f - 2.0f * (1.0f - t) * (1.0f - t);
        }

        float EaseInCubic(float t) {
            return t * t * t;
        }

        float EaseOutCubic(float t) {
            float f = 1.0f - t;
            return 1.0f - f * f * f;
        }

        float EaseInOutCubic(float t) {
            return t < 0.5f ? 4.0f * t * t * t : 1.0f - 4.0f * (1.0f - t) * (1.0f - t) * (1.0f - t);
        }
    }

    // Transition utility functions
    namespace Transitions {
        float SmoothHover(float& currentValue, bool isHovered, float speed, float deltaTime) {
            float target = isHovered ? 1.0f : 0.0f;
            currentValue = GetAnimationSystem().SmoothStep(currentValue, target, speed, deltaTime);
            return currentValue;
        }

        float SmoothStep(float currentValue, float target, float speed, float deltaTime) {
            return GetAnimationSystem().SmoothStep(currentValue, target, speed, deltaTime);
        }

        float PulseAnimation(float time, float frequency, float amplitude) {
            return 1.0f + amplitude * std::sin(time * frequency * 2.0f * 3.14159f);
        }

        float FadeTransition(float& currentAlpha, bool fadeIn, float speed, float deltaTime) {
            float target = fadeIn ? 1.0f : 0.0f;
            currentAlpha = GetAnimationSystem().SmoothStep(currentAlpha, target, speed, deltaTime);
            return std::clamp(currentAlpha, 0.0f, 1.0f);
        }

        float ScaleTransition(float& currentScale, bool isPressed, float speed, float deltaTime) {
            float target = isPressed ? 0.95f : 1.0f;
            currentScale = GetAnimationSystem().SmoothStep(currentScale, target, speed, deltaTime);
            return currentScale;
        }

        float SpinnerAnimation(float time, float speed) {
            return fmod(time * speed, 2.0f * 3.14159f);
        }

        float ProgressBarAnimation(float progress, float& currentProgress, float speed, float deltaTime) {
            currentProgress = GetAnimationSystem().SmoothStep(currentProgress, progress, speed, deltaTime);
            return currentProgress;
        }

        float DotsLoadingAnimation(float time, int dotCount, float speed) {
            float cycle = fmod(time * speed, static_cast<float>(dotCount));
            return cycle;
        }

        float SecurityScanAnimation(float time, float progress) {
            float baseAnimation = std::sin(time * 3.0f) * 0.1f + 0.9f;
            return baseAnimation * progress;
        }

        float ThreatPulseAnimation(float time, float intensity) {
            return (std::sin(time * 4.0f) * 0.5f + 0.5f) * intensity;
        }
    }

    // Loading animation components implementation
    namespace LoadingAnimations {
        void RenderSpinner(const ImVec2& center, float radius, float thickness,
                          const ImVec4& color, float speed) {
            static auto startTime = std::chrono::steady_clock::now();
            auto currentTime = std::chrono::steady_clock::now();
            float time = std::chrono::duration<float>(currentTime - startTime).count();

            float rotation = Transitions::SpinnerAnimation(time, speed);

            ImDrawList* drawList = ImGui::GetWindowDrawList();

            // Draw spinning arc
            int segments = 30;
            float arcLength = 4.0f; // Length of the arc in radians

            for (int i = 0; i < segments; ++i) {
                float angle1 = rotation + (static_cast<float>(i) / segments) * arcLength;
                float angle2 = rotation + (static_cast<float>(i + 1) / segments) * arcLength;

                ImVec2 p1(center.x + std::cos(angle1) * radius, center.y + std::sin(angle1) * radius);
                ImVec2 p2(center.x + std::cos(angle2) * radius, center.y + std::sin(angle2) * radius);

                float alpha = static_cast<float>(i) / segments;
                ImVec4 segmentColor = color;
                segmentColor.w *= alpha;

                drawList->AddLine(p1, p2, ImGui::ColorConvertFloat4ToU32(segmentColor), thickness);
            }
        }

        void RenderProgressBar(const ImVec2& position, const ImVec2& size, float progress,
                              const ImVec4& backgroundColor, const ImVec4& progressColor, bool animated) {
            ImDrawList* drawList = ImGui::GetWindowDrawList();

            // Background
            drawList->AddRectFilled(position, ImVec2(position.x + size.x, position.y + size.y),
                                  ImGui::ColorConvertFloat4ToU32(backgroundColor));

            // Progress bar
            static float animatedProgress = 0.0f;
            if (animated) {
                static auto lastTime = std::chrono::steady_clock::now();
                auto currentTime = std::chrono::steady_clock::now();
                float deltaTime = std::chrono::duration<float>(currentTime - lastTime).count();
                lastTime = currentTime;

                animatedProgress = Transitions::ProgressBarAnimation(progress, animatedProgress, 3.0f, deltaTime);
            } else {
                animatedProgress = progress;
            }

            float progressWidth = size.x * std::clamp(animatedProgress, 0.0f, 1.0f);
            if (progressWidth > 0) {
                drawList->AddRectFilled(position, ImVec2(position.x + progressWidth, position.y + size.y),
                                      ImGui::ColorConvertFloat4ToU32(progressColor));
            }

            // Add shimmer effect if animated
            if (animated && animatedProgress > 0.0f && animatedProgress < 1.0f) {
                static auto startTime = std::chrono::steady_clock::now();
                auto currentTime = std::chrono::steady_clock::now();
                float time = std::chrono::duration<float>(currentTime - startTime).count();

                float shimmerPos = fmod(time * 0.5f, 1.0f) * size.x;
                ImVec4 shimmerColor = progressColor;
                shimmerColor.w *= 0.3f;

                drawList->AddRectFilled(ImVec2(position.x + shimmerPos - 10, position.y),
                                      ImVec2(position.x + shimmerPos + 10, position.y + size.y),
                                      ImGui::ColorConvertFloat4ToU32(shimmerColor));
            }
        }

        void RenderLoadingDots(const ImVec2& position, const std::string& baseText,
                              const ImVec4& color, float speed) {
            static auto startTime = std::chrono::steady_clock::now();
            auto currentTime = std::chrono::steady_clock::now();
            float time = std::chrono::duration<float>(currentTime - startTime).count();

            float dotCycle = Transitions::DotsLoadingAnimation(time, 3, speed);
            int activeDots = static_cast<int>(dotCycle) + 1;

            std::string displayText = baseText;
            for (int i = 0; i < activeDots; ++i) {
                displayText += ".";
            }

            ImGui::SetCursorPos(position);
            ImGui::TextColored(color, "%s", displayText.c_str());
        }

        void RenderSecurityScanAnimation(const ImVec2& position, const ImVec2& size,
                                       float progress, const std::string& scanType) {
            static auto startTime = std::chrono::steady_clock::now();
            auto currentTime = std::chrono::steady_clock::now();
            float time = std::chrono::duration<float>(currentTime - startTime).count();

            ImGui::SetCursorPos(position);

            // Animated scan text
            RenderLoadingDots(position, scanType, ImVec4(0.8f, 0.8f, 1.0f, 1.0f));

            // Progress bar with security-themed colors
            ImVec2 progressPos(position.x, position.y + 25);
            ImVec2 progressSize(size.x, 8);

            ImVec4 bgColor(0.1f, 0.1f, 0.2f, 1.0f);
            ImVec4 progressColor = progress < 0.5f ? ImVec4(1.0f, 0.8f, 0.0f, 1.0f) : // Yellow for scanning
                                  progress < 0.9f ? ImVec4(0.0f, 0.8f, 1.0f, 1.0f) : // Blue for analyzing
                                                   ImVec4(0.0f, 1.0f, 0.2f, 1.0f);   // Green for complete

            RenderProgressBar(progressPos, progressSize, progress, bgColor, progressColor, true);

            // Security scan effect
            float scanIntensity = Transitions::SecurityScanAnimation(time, progress);
            if (progress > 0.0f && progress < 1.0f) {
                ImDrawList* drawList = ImGui::GetWindowDrawList();

                // Scanning line effect
                float scanLinePos = progress * size.x;
                ImVec2 lineStart(position.x + scanLinePos, progressPos.y);
                ImVec2 lineEnd(position.x + scanLinePos, progressPos.y + progressSize.y);

                ImVec4 scanLineColor(1.0f, 1.0f, 1.0f, scanIntensity);
                drawList->AddLine(lineStart, lineEnd, ImGui::ColorConvertFloat4ToU32(scanLineColor), 2.0f);
            }
        }
    }

} // namespace Animation
} // namespace ui