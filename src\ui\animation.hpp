#pragma once

#include <functional>
#include <chrono>
#include <map>
#include <string>

namespace ui {
namespace Animation {

    // Easing function types
    enum class EasingType {
        Linear,
        EaseIn,
        EaseOut,
        EaseInOut,
        EaseInQuad,
        EaseOutQuad,
        EaseInOutQuad,
        EaseInCubic,
        EaseOutCubic,
        EaseInOutCubic
    };

    // Animation state for individual animations
    struct AnimationState {
        float startValue = 0.0f;
        float endValue = 1.0f;
        float currentValue = 0.0f;
        float duration = 1.0f;
        float elapsed = 0.0f;
        EasingType easing = EasingType::EaseInOut;
        bool isPlaying = false;
        bool isReversed = false;
        bool loop = false;
        std::function<void(float)> onUpdate = nullptr;
        std::function<void()> onComplete = nullptr;
    };

    // Animation system class
    class AnimationSystem {
    public:
        // Create or get an animation
        AnimationState& CreateAnimation(const std::string& id);
        AnimationState* GetAnimation(const std::string& id);
        
        // Control animations
        void PlayAnimation(const std::string& id);
        void PauseAnimation(const std::string& id);
        void StopAnimation(const std::string& id);
        void ReverseAnimation(const std::string& id);
        
        // Update all animations (call every frame)
        void Update(float deltaTime);
        
        // Remove animations
        void RemoveAnimation(const std::string& id);
        void ClearAllAnimations();
        
        // Utility functions for common animations
        void AnimateFloat(const std::string& id, float& target, float endValue, 
                         float duration, EasingType easing = EasingType::EaseInOut);
        void AnimateFloatFromTo(const std::string& id, float& target, float startValue, 
                               float endValue, float duration, EasingType easing = EasingType::EaseInOut);
        
        // Smooth transitions
        float SmoothStep(float value, float target, float speed, float deltaTime);
        float Lerp(float a, float b, float t);
        
    private:
        std::map<std::string, AnimationState> m_animations;
        
        // Easing functions
        float ApplyEasing(float t, EasingType easing);
    };

    // Global animation system instance
    AnimationSystem& GetAnimationSystem();

    // Easing function implementations
    namespace Easing {
        float Linear(float t);
        float EaseIn(float t);
        float EaseOut(float t);
        float EaseInOut(float t);
        float EaseInQuad(float t);
        float EaseOutQuad(float t);
        float EaseInOutQuad(float t);
        float EaseInCubic(float t);
        float EaseOutCubic(float t);
        float EaseInOutCubic(float t);
    }

    // Utility functions for smooth UI transitions
    namespace Transitions {
        // Smooth hover effects
        float SmoothHover(float& currentValue, bool isHovered, float speed = 8.0f, float deltaTime = 0.016f);

        // Smooth step transition
        float SmoothStep(float currentValue, float target, float speed, float deltaTime);

        // Pulsing animation for status indicators
        float PulseAnimation(float time, float frequency = 2.0f, float amplitude = 0.3f);

        // Fade in/out effects
        float FadeTransition(float& currentAlpha, bool fadeIn, float speed = 4.0f, float deltaTime = 0.016f);

        // Scale animation for buttons
        float ScaleTransition(float& currentScale, bool isPressed, float speed = 10.0f, float deltaTime = 0.016f);

        // Loading animations
        float SpinnerAnimation(float time, float speed = 2.0f);
        float ProgressBarAnimation(float progress, float& currentProgress, float speed = 3.0f, float deltaTime = 0.016f);
        float DotsLoadingAnimation(float time, int dotCount = 3, float speed = 1.5f);

        // Security-specific animations
        float SecurityScanAnimation(float time, float progress = 0.0f);
        float ThreatPulseAnimation(float time, float intensity = 1.0f);
    }

    // Loading animation components
    namespace LoadingAnimations {
        // Render a spinning loading indicator
        void RenderSpinner(const ImVec2& center, float radius, float thickness = 3.0f,
                          const ImVec4& color = ImVec4(1, 1, 1, 1), float speed = 2.0f);

        // Render animated progress bar
        void RenderProgressBar(const ImVec2& position, const ImVec2& size, float progress,
                              const ImVec4& backgroundColor = ImVec4(0.2f, 0.2f, 0.2f, 1.0f),
                              const ImVec4& progressColor = ImVec4(0.0f, 0.8f, 0.2f, 1.0f),
                              bool animated = true);

        // Render animated dots (...)
        void RenderLoadingDots(const ImVec2& position, const std::string& baseText = "Loading",
                              const ImVec4& color = ImVec4(1, 1, 1, 1), float speed = 1.5f);

        // Render security scan animation
        void RenderSecurityScanAnimation(const ImVec2& position, const ImVec2& size,
                                       float progress, const std::string& scanType = "Security Scan");
    }

} // namespace Animation
} // namespace ui