#pragma once

// Prevent Windows socket header conflicts
#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif
#ifndef NOMINMAX
#define NOMINMAX
#endif

#include "ui.hpp"
#include "theme.hpp"
#include "animation.hpp"
#include <chrono>
#include <algorithm>

// Prevent Windows macro conflicts
#ifdef min
#undef min
#endif
#ifdef max
#undef max
#endif

namespace ui {

    // Forward declarations
    class StatusIndicator;
    class SecurityStatusSummary;

    // Base class for all UI components
    class BaseComponent : public UIComponent {
    public:
        BaseComponent(const std::string& id) : m_id(id) {}
        virtual ~BaseComponent() = default;

        const std::string& GetId() const { return m_id; }
        
        virtual void Render() override = 0;
        virtual void Update(float deltaTime) override {}

    protected:
        std::string m_id;
    };

    // Modern button component interface
    class ModernButton : public BaseComponent {
    public:
        enum class Style {
            Primary,
            Secondary,
            Danger
        };

        ModernButton(const std::string& id, const std::string& label) 
            : BaseComponent(id), m_label(label) {}
        
        virtual void Render() override; // Base implementation for abstract class
        virtual bool Render(const ImVec2& size);
        virtual void SetStyle(Style style) { m_style = style; }
        virtual void SetLabel(const std::string& label) { m_label = label; }

    protected:
        std::string m_label;
        Style m_style = Style::Primary;
        float m_hoverAnimation = 0.0f;
    };

    // Status indicator component interface
    class StatusIndicator : public BaseComponent {
    public:
        enum class Status {
            Unknown,
            Checking,
            Secure,
            Warning,
            Critical
        };

        StatusIndicator(const std::string& id) : BaseComponent(id) {}
        
        virtual void Render() override;
        virtual void Update(float deltaTime) override;
        virtual void SetStatus(Status status) { m_status = status; }
        virtual void SetAnimated(bool animated) { m_animated = animated; }
        virtual Status GetStatus() const { return m_status; }
        virtual void SetSize(float size) { m_size = size; }
        virtual void SetShowText(bool showText) { m_showText = showText; }

    protected:
        Status m_status = Status::Unknown;
        bool m_animated = true;
        bool m_showText = true;
        float m_pulseAnimation = 0.0f;
        float m_size = 8.0f;
        float m_glowIntensity = 0.0f;
    };

    // Window controls component
    class WindowControls : public BaseComponent {
    public:
        WindowControls() : BaseComponent("window_controls") {}
        
        virtual void Render() override;
        virtual void Update(float deltaTime) override;
        virtual void SetShowMinimize(bool show) { m_showMinimize = show; }
        virtual void SetShowMaximize(bool show) { m_showMaximize = show; }
        virtual void SetShowClose(bool show) { m_showClose = show; }
        virtual void SetShowPresets(bool show) { m_showPresets = show; }
        virtual bool ShouldClose() const { return m_shouldClose; }
        virtual void ResetCloseFlag() { m_shouldClose = false; }

    protected:
        bool m_showMinimize = true;
        bool m_showMaximize = true;
        bool m_showClose = true;
        bool m_showPresets = false;
        bool m_shouldClose = false;
        float m_hoverAnimation = 0.0f;
        
        void RenderControlButton(const char* icon, const char* tooltip, bool& clicked, ImVec4 color = ImVec4(1, 1, 1, 1));
        void RenderPresetDropdown();
    };

    // Header component interface
    class HeaderComponent : public BaseComponent {
    public:
        HeaderComponent() : BaseComponent("header"), m_statusIndicator("header_status"), m_windowControls() {}
        
        virtual void Render() override;
        virtual void Update(float deltaTime) override;
        virtual void SetTitle(const std::string& title) { m_title = title; }
        virtual void SetSecurityStatus(const std::string& status);
        virtual void SetSubtitle(const std::string& subtitle) { m_subtitle = subtitle; }
        virtual void SetHeight(float height) { m_height = height; }
        virtual void SetShowBorder(bool showBorder) { m_showBorder = showBorder; }
        virtual void SetShowWindowControls(bool show) { m_showWindowControls = show; }
        virtual bool ShouldClose() const { return m_windowControls.ShouldClose(); }
        virtual void ResetCloseFlag() { m_windowControls.ResetCloseFlag(); }

    protected:
        std::string m_title = "Nebula Loader";
        std::string m_subtitle = "";
        std::string m_securityStatus = "Active";
        StatusIndicator m_statusIndicator;
        WindowControls m_windowControls;
        float m_height = 60.0f;
        bool m_showBorder = true;
        bool m_showWindowControls = true;
        float m_titleFadeIn = 0.0f;
    };

    // Security check data structure
    struct SecurityCheck {
        std::string name;
        std::string description;
        StatusIndicator::Status status;
        std::string details;
        float progress = 0.0f;
        std::chrono::steady_clock::time_point lastUpdate;
        bool isExpanded = false;
        bool isEnabled = true;

        SecurityCheck(const std::string& n, const std::string& desc)
            : name(n), description(desc), status(StatusIndicator::Status::Unknown),
              lastUpdate(std::chrono::steady_clock::now()) {}
    };

    // Security check card component
    class SecurityCheckCard : public BaseComponent {
    public:
        SecurityCheckCard(const std::string& id, const SecurityCheck& check) 
            : BaseComponent(id), m_check(check) {}
        
        virtual void Render() override;
        virtual void Update(float deltaTime) override;
        virtual void SetCheck(const SecurityCheck& check) { m_check = check; }
        virtual const SecurityCheck& GetCheck() const { return m_check; }
        virtual void SetExpanded(bool expanded) { m_check.isExpanded = expanded; }

    protected:
        SecurityCheck m_check;
        float m_expandAnimation = 0.0f;
        float m_hoverAnimation = 0.0f;
    };

    // Removed SecurityRecommendation structure - this is a loader, not a security management system

    // Security status summary component
    class SecurityStatusSummary : public BaseComponent {
    public:
        SecurityStatusSummary() : BaseComponent("security_status_summary") {}
        
        virtual void Render() override;
        virtual void Update(float deltaTime) override;
        virtual void SetOverallStatus(StatusIndicator::Status status) { m_overallStatus = status; }
        virtual void SetSecurityLevel(float level) { 
            m_securityLevel = (level < 0.0f) ? 0.0f : (level > 1.0f) ? 1.0f : level; 
        }
        // Removed recommendation methods - this is a loader, not a security management system

    protected:
        StatusIndicator::Status m_overallStatus = StatusIndicator::Status::Unknown;
        float m_securityLevel = 0.0f;
        float m_animationTime = 0.0f;

        // Helper methods (removed recommendation-related methods)
    };

    // Security dashboard component
    class SecurityDashboard : public BaseComponent {
    public:
        SecurityDashboard() : BaseComponent("security_dashboard") {
            m_statusSummary = std::make_unique<SecurityStatusSummary>();
        }
        
        virtual void Render() override;
        virtual void Update(float deltaTime) override;
        virtual void UpdateSecurityChecks(const std::vector<SecurityCheck>& checks);
        virtual void SetOverallStatus(StatusIndicator::Status status) { m_overallStatus = status; }
        virtual StatusIndicator::Status GetOverallStatus() const { return m_overallStatus; }
        virtual void AddSecurityCheck(const SecurityCheck& check);
        virtual void UpdateSecurityCheck(const std::string& name, StatusIndicator::Status status, const std::string& details = "");
        virtual void SetProgress(const std::string& name, float progress);

    protected:
        std::vector<std::unique_ptr<SecurityCheckCard>> m_checkCards;
        std::vector<SecurityCheck> m_securityChecks;
        StatusIndicator::Status m_overallStatus = StatusIndicator::Status::Unknown;
        float m_animationProgress = 0.0f;
        float m_lastCheckTime = 0.0f;
        std::unique_ptr<SecurityStatusSummary> m_statusSummary;
        
        // Helper methods
        void CreateCheckCards();
        StatusIndicator::Status CalculateOverallStatus() const;
        ImVec4 GetStatusColor(StatusIndicator::Status status) const;
        const char* GetStatusText(StatusIndicator::Status status) const;
        const char* GetStatusIcon(StatusIndicator::Status status) const;
    };

    // Footer component
    class FooterComponent : public BaseComponent {
    public:
        FooterComponent() : BaseComponent("footer") {
            m_lastCheckTime = std::chrono::steady_clock::now();
        }
        
        virtual void Render() override;
        virtual void Update(float deltaTime) override;
        virtual void SetVersion(const std::string& version) { m_version = version; }
        virtual void SetStatus(const std::string& status) { m_status = status; }
        virtual void SetLastCheckTime(const std::chrono::steady_clock::time_point& time) { m_lastCheckTime = time; }
        virtual void SetHeight(float height) { m_height = height; }
        virtual void SetBuildInfo(const std::string& buildInfo) { m_buildInfo = buildInfo; }
        virtual void SetConnectionStatus(const std::string& connectionStatus) { m_connectionStatus = connectionStatus; }
        virtual void SetMemoryUsage(float memoryMB) { m_memoryUsage = memoryMB; }
        virtual void SetCpuUsage(float cpuPercent) { m_cpuUsage = cpuPercent; }
        virtual void SetShowDetailedInfo(bool show) { m_showDetailedInfo = show; }

    protected:
        std::string m_version = "v1.0.0";
        std::string m_status = "Ready";
        std::string m_buildInfo = "Build 2024.01.01";
        std::string m_connectionStatus = "Connected";
        std::chrono::steady_clock::time_point m_lastCheckTime;
        float m_height = 30.0f;
        float m_fadeAnimation = 0.0f;
        float m_memoryUsage = 0.0f;
        float m_cpuUsage = 0.0f;
        bool m_showDetailedInfo = false;
        
        // Helper methods
        std::string FormatTimeAgo(const std::chrono::steady_clock::time_point& time) const;
        std::string FormatMemoryUsage(float memoryMB) const;
        void RenderCompactFooter();
        void RenderDetailedFooter();
        bool IsCompactMode() const;
    };

    // Window state for persistence
    struct WindowState {
        ImVec2 position = ImVec2(100, 100);
        ImVec2 size = ImVec2(800, 600);
        bool isMaximized = false;
        bool isMinimized = false;
        float uiScale = 1.0f;
        std::string lastSavedTime;
        
        // Validation
        bool IsValid() const {
            return size.x >= 320.0f && size.y >= 240.0f && 
                   position.x >= -100.0f && position.y >= -100.0f &&
                   uiScale >= 0.5f && uiScale <= 3.0f;
        }
    };

    // Main window layout component
    class MainWindowLayout : public BaseComponent {
    public:
        MainWindowLayout() : BaseComponent("main_window_layout") {
            m_header = std::make_unique<HeaderComponent>();
            m_footer = std::make_unique<FooterComponent>();
            m_securityDashboard = std::make_unique<SecurityDashboard>();
            
            // Load window state
            LoadWindowState();
        }
        
        ~MainWindowLayout() {
            // Save window state on destruction
            SaveWindowState();
        }
        
        virtual void Render() override;
        virtual void Update(float deltaTime) override;
        virtual void SetWindowSize(const ImVec2& size) { m_windowSize = size; }
        virtual void SetWindowPosition(const ImVec2& position) { m_windowPosition = position; }
        virtual ImVec2 GetWindowSize() const { return m_windowSize; }
        virtual ImVec2 GetWindowPosition() const { return m_windowPosition; }
        virtual void SetContentPadding(float padding) { m_contentPadding = padding; }
        virtual void SetResponsiveBreakpoints(float smallBreakpoint, float mediumBreakpoint) {
            m_smallBreakpoint = smallBreakpoint;
            m_mediumBreakpoint = mediumBreakpoint;
        }
        
        // Window management
        virtual void SaveWindowState();
        virtual void LoadWindowState();
        virtual void SetWindowConstraints(ImVec2 minSize, ImVec2 maxSize);
        virtual void HandleWindowEvents();
        virtual bool IsWindowStateChanged() const;
        
        // Component access
        virtual HeaderComponent* GetHeader() { return m_header.get(); }
        virtual FooterComponent* GetFooter() { return m_footer.get(); }
        virtual SecurityDashboard* GetSecurityDashboard() { return m_securityDashboard.get(); }

    protected:
        std::unique_ptr<HeaderComponent> m_header;
        std::unique_ptr<FooterComponent> m_footer;
        std::unique_ptr<SecurityDashboard> m_securityDashboard;
        
        ImVec2 m_windowSize = ImVec2(800, 600);
        ImVec2 m_windowPosition = ImVec2(100, 100);
        float m_contentPadding = 16.0f;
        float m_smallBreakpoint = 480.0f;
        float m_mediumBreakpoint = 768.0f;
        
        // Layout state
        bool m_isCompactMode = false;
        float m_headerHeight = 60.0f;
        float m_footerHeight = 30.0f;
        float m_layoutAnimation = 0.0f;
        
        // Window management state
        WindowState m_windowState;
        ImVec2 m_lastFrameSize = ImVec2(0, 0);
        ImVec2 m_lastFramePosition = ImVec2(0, 0);
        bool m_windowStateChanged = false;
        float m_stateSaveTimer = 0.0f;
        static constexpr float STATE_SAVE_DELAY = 1.0f; // Save state 1 second after last change
        
        // Helper methods
        void UpdateResponsiveLayout();
        void RenderContent();
        float GetContentHeight() const;
        bool IsSmallScreen() const { return m_windowSize.x < m_smallBreakpoint; }
        bool IsMediumScreen() const { return m_windowSize.x < m_mediumBreakpoint; }
    };

} // namespace ui