#include "performance_monitor.hpp"
#include "theme.hpp"
#include <imgui.h>
#include <algorithm>
#include <numeric>
#include <windows.h>
#include <psapi.h>
#include <fstream>

namespace ui {

    PerformanceMonitor& PerformanceMonitor::GetInstance() {
        static PerformanceMonitor instance;
        return instance;
    }

    void PerformanceMonitor::BeginFrame() {
        m_frameStartTime = std::chrono::steady_clock::now();
    }

    void PerformanceMonitor::EndFrame() {
        auto frameEndTime = std::chrono::steady_clock::now();
        float frameTime = std::chrono::duration<float, std::milli>(frameEndTime - m_frameStartTime).count();
        
        // Store frame time
        if (m_frameTimes.size() < MAX_FRAME_SAMPLES) {
            m_frameTimes.push_back(frameTime);
        } else {
            m_frameTimes[m_frameTimeIndex] = frameTime;
            m_frameTimeIndex = (m_frameTimeIndex + 1) % MAX_FRAME_SAMPLES;
        }
        
        // Update current metrics
        std::lock_guard<std::mutex> lock(m_metricsMutex);
        m_currentMetrics.frameTime = frameTime;
        m_currentMetrics.frameRate = frameTime > 0.0f ? 1000.0f / frameTime : 0.0f;
        m_currentMetrics.timestamp = frameEndTime;
        
        UpdateAverageFrameRate();
        
        // Store metrics in history
        m_metricsHistory.push_back(m_currentMetrics);
        TrimMetricsHistory();
        
        // Call performance callback
        if (m_performanceCallback) {
            try {
                m_performanceCallback(m_currentMetrics);
            } catch (...) {
                // Ignore callback exceptions
            }
        }
        
        m_lastFrameTime = frameEndTime;
    }

    void PerformanceMonitor::UpdateFrameStats() {
        std::lock_guard<std::mutex> lock(m_metricsMutex);
        
        if (!m_frameTimes.empty()) {
            auto minMax = std::minmax_element(m_frameTimes.begin(), m_frameTimes.end());
            m_currentMetrics.minFrameRate = *minMax.second > 0.0f ? 1000.0f / *minMax.second : 0.0f;
            m_currentMetrics.maxFrameRate = *minMax.first > 0.0f ? 1000.0f / *minMax.first : 0.0f;
        }
    }

    void PerformanceMonitor::UpdateMemoryUsage() {
        PROCESS_MEMORY_COUNTERS pmc;
        if (GetProcessMemoryInfo(GetCurrentProcess(), &pmc, sizeof(pmc))) {
            std::lock_guard<std::mutex> lock(m_metricsMutex);
            m_currentMetrics.memoryUsage = pmc.WorkingSetSize;
        }
    }

    size_t PerformanceMonitor::GetCurrentMemoryUsage() const {
        std::lock_guard<std::mutex> lock(m_metricsMutex);
        return m_currentMetrics.memoryUsage;
    }

    void PerformanceMonitor::UpdateCPUUsage() {
        // Simplified CPU usage calculation
        static ULARGE_INTEGER lastCPU, lastSysCPU, lastUserCPU;
        static int numProcessors = 0;
        static HANDLE self = GetCurrentProcess();
        
        if (numProcessors == 0) {
            SYSTEM_INFO sysInfo;
            GetSystemInfo(&sysInfo);
            numProcessors = sysInfo.dwNumberOfProcessors;
        }
        
        FILETIME ftime, fsys, fuser;
        ULARGE_INTEGER now, sys, user;
        
        GetSystemTimeAsFileTime(&ftime);
        memcpy(&now, &ftime, sizeof(FILETIME));
        
        GetProcessTimes(self, &ftime, &ftime, &fsys, &fuser);
        memcpy(&sys, &fsys, sizeof(FILETIME));
        memcpy(&user, &fuser, sizeof(FILETIME));
        
        if (lastCPU.QuadPart != 0) {
            double percent = (sys.QuadPart - lastSysCPU.QuadPart) + (user.QuadPart - lastUserCPU.QuadPart);
            percent /= (now.QuadPart - lastCPU.QuadPart);
            percent /= numProcessors;
            
            std::lock_guard<std::mutex> lock(m_metricsMutex);
            m_currentMetrics.cpuUsage = static_cast<float>(percent * 100.0);
        }
        
        lastCPU = now;
        lastUserCPU = user;
        lastSysCPU = sys;
    }

    float PerformanceMonitor::GetCurrentCPUUsage() const {
        std::lock_guard<std::mutex> lock(m_metricsMutex);
        return m_currentMetrics.cpuUsage;
    }

    void PerformanceMonitor::RecordDrawCall(size_t triangles, size_t vertices) {
        std::lock_guard<std::mutex> lock(m_metricsMutex);
        m_currentMetrics.drawCalls++;
        m_currentMetrics.triangles += triangles;
        m_currentMetrics.vertices += vertices;
    }

    void PerformanceMonitor::ResetRenderingStats() {
        std::lock_guard<std::mutex> lock(m_metricsMutex);
        m_currentMetrics.drawCalls = 0;
        m_currentMetrics.triangles = 0;
        m_currentMetrics.vertices = 0;
    }

    const PerformanceMetrics& PerformanceMonitor::GetCurrentMetrics() const {
        std::lock_guard<std::mutex> lock(m_metricsMutex);
        return m_currentMetrics;
    }

    std::vector<PerformanceMetrics> PerformanceMonitor::GetRecentMetrics(size_t count) const {
        std::lock_guard<std::mutex> lock(m_metricsMutex);
        
        std::vector<PerformanceMetrics> recent;
        size_t startIndex = m_metricsHistory.size() > count ? m_metricsHistory.size() - count : 0;
        
        for (size_t i = startIndex; i < m_metricsHistory.size(); ++i) {
            recent.push_back(m_metricsHistory[i]);
        }
        
        return recent;
    }

    bool PerformanceMonitor::ShouldOptimizeRendering() const {
        std::lock_guard<std::mutex> lock(m_metricsMutex);
        return m_currentMetrics.frameRate < m_targetFrameRate * 0.8f || m_currentMetrics.cpuUsage > 80.0f;
    }

    bool PerformanceMonitor::ShouldReduceAnimations() const {
        std::lock_guard<std::mutex> lock(m_metricsMutex);
        return m_currentMetrics.frameRate < m_targetFrameRate * 0.6f || m_currentMetrics.cpuUsage > 90.0f;
    }

    bool PerformanceMonitor::ShouldLimitUpdates() const {
        std::lock_guard<std::mutex> lock(m_metricsMutex);
        return m_currentMetrics.frameRate < m_targetFrameRate * 0.5f || m_currentMetrics.cpuUsage > 95.0f;
    }

    void PerformanceMonitor::SetTargetFrameRate(float targetFPS) {
        m_targetFrameRate = std::max(1.0f, targetFPS);
    }

    void PerformanceMonitor::SetPerformanceMode(bool highPerformance) {
        m_highPerformanceMode = highPerformance;
        if (highPerformance) {
            m_targetFrameRate = 120.0f;
        } else {
            m_targetFrameRate = 60.0f;
        }
    }

    void PerformanceMonitor::SetVSync(bool enabled) {
        m_vsyncEnabled = enabled;
    }

    void PerformanceMonitor::RegisterPerformanceCallback(PerformanceCallback callback) {
        m_performanceCallback = callback;
    }

    void PerformanceMonitor::RenderPerformanceOverlay() {
        const auto& theme = Theme::GetCurrentTheme();
        const auto& metrics = GetCurrentMetrics();

        // Position overlay in top-right corner
        ImVec2 overlayPos(ImGui::GetIO().DisplaySize.x - 250, 10);
        ImGui::SetNextWindowPos(overlayPos, ImGuiCond_Always);
        ImGui::SetNextWindowSize(ImVec2(240, 120), ImGuiCond_Always);

        ImGuiWindowFlags flags = ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoResize |
                                ImGuiWindowFlags_NoMove | ImGuiWindowFlags_NoScrollbar |
                                ImGuiWindowFlags_NoCollapse | ImGuiWindowFlags_AlwaysAutoResize;

        ImGui::PushStyleColor(ImGuiCol_WindowBg, ImVec4(0.0f, 0.0f, 0.0f, 0.7f));

        if (ImGui::Begin("Performance Overlay", nullptr, flags)) {
            // Frame rate
            ImVec4 fpsColor = metrics.frameRate >= m_targetFrameRate * 0.9f ? theme.success :
                             metrics.frameRate >= m_targetFrameRate * 0.6f ? theme.warning : theme.danger;
            ImGui::TextColored(fpsColor, "FPS: %.1f (%.1f ms)", metrics.frameRate, metrics.frameTime);

            // Memory usage
            float memoryMB = static_cast<float>(metrics.memoryUsage) / (1024.0f * 1024.0f);
            ImGui::Text("Memory: %.1f MB", memoryMB);

            // CPU usage
            ImVec4 cpuColor = metrics.cpuUsage < 50.0f ? theme.success :
                             metrics.cpuUsage < 80.0f ? theme.warning : theme.danger;
            ImGui::TextColored(cpuColor, "CPU: %.1f%%", metrics.cpuUsage);

            // Rendering stats
            ImGui::Text("Draw Calls: %zu", metrics.drawCalls);
        }
        ImGui::End();

        ImGui::PopStyleColor();
    }

    void PerformanceMonitor::RenderPerformanceGraph() {
        const auto& theme = Theme::GetCurrentTheme();
        auto recentMetrics = GetRecentMetrics(60); // Last 60 frames

        if (recentMetrics.empty()) return;

        ImGui::SetNextWindowSize(ImVec2(400, 300), ImGuiCond_FirstUseEver);

        if (ImGui::Begin("Performance Graph")) {
            // Frame rate graph
            std::vector<float> frameRates;
            frameRates.reserve(recentMetrics.size());
            for (const auto& metric : recentMetrics) {
                frameRates.push_back(metric.frameRate);
            }

            ImGui::PlotLines("Frame Rate", frameRates.data(), static_cast<int>(frameRates.size()),
                           0, nullptr, 0.0f, m_targetFrameRate * 2.0f, ImVec2(0, 80));

            // Memory usage graph
            std::vector<float> memoryUsage;
            memoryUsage.reserve(recentMetrics.size());
            for (const auto& metric : recentMetrics) {
                memoryUsage.push_back(static_cast<float>(metric.memoryUsage) / (1024.0f * 1024.0f));
            }

            ImGui::PlotLines("Memory (MB)", memoryUsage.data(), static_cast<int>(memoryUsage.size()),
                           0, nullptr, 0.0f, 0.0f, ImVec2(0, 80));

            // CPU usage graph
            std::vector<float> cpuUsage;
            cpuUsage.reserve(recentMetrics.size());
            for (const auto& metric : recentMetrics) {
                cpuUsage.push_back(metric.cpuUsage);
            }

            ImGui::PlotLines("CPU Usage (%)", cpuUsage.data(), static_cast<int>(cpuUsage.size()),
                           0, nullptr, 0.0f, 100.0f, ImVec2(0, 80));
        }
        ImGui::End();
    }

    void PerformanceMonitor::UpdateAverageFrameRate() {
        if (!m_frameTimes.empty()) {
            float avgFrameTime = CalculateAverageFrameTime();
            m_currentMetrics.averageFrameRate = avgFrameTime > 0.0f ? 1000.0f / avgFrameTime : 0.0f;
        }
    }

    void PerformanceMonitor::TrimMetricsHistory() {
        if (m_metricsHistory.size() > MAX_METRICS_HISTORY) {
            m_metricsHistory.erase(m_metricsHistory.begin(), 
                                 m_metricsHistory.begin() + (m_metricsHistory.size() - MAX_METRICS_HISTORY));
        }
    }

    float PerformanceMonitor::CalculateAverageFrameTime() const {
        if (m_frameTimes.empty()) return 0.0f;
        
        float sum = std::accumulate(m_frameTimes.begin(), m_frameTimes.end(), 0.0f);
        return sum / static_cast<float>(m_frameTimes.size());
    }

    // Performance utilities implementation
    namespace PerformanceUtils {
        bool ShouldSkipFrame(float targetFPS, float currentFPS) {
            return currentFPS > targetFPS * 1.5f;
        }

        bool ShouldReduceQuality(const PerformanceMetrics& metrics) {
            return metrics.frameRate < 30.0f || metrics.cpuUsage > 85.0f;
        }

        bool ShouldBatchDrawCalls(size_t currentDrawCalls) {
            return currentDrawCalls > 100;
        }

        float GetOptimalAnimationSpeed(const PerformanceMetrics& metrics) {
            if (metrics.frameRate < 30.0f) return 0.5f;
            if (metrics.frameRate < 45.0f) return 0.75f;
            return 1.0f;
        }

        bool ShouldDisableAnimations(const PerformanceMetrics& metrics) {
            return metrics.frameRate < 20.0f || metrics.cpuUsage > 95.0f;
        }

        bool ShouldGarbageCollect(size_t currentMemory, size_t maxMemory) {
            return currentMemory > maxMemory * 0.8;
        }

        size_t GetOptimalCacheSize(const PerformanceMetrics& metrics) {
            size_t baseSize = 1024 * 1024; // 1MB base
            if (metrics.memoryUsage > 100 * 1024 * 1024) { // > 100MB
                return baseSize / 2;
            }
            return baseSize;
        }

        bool ShouldReduceUpdateFrequency(float cpuUsage) {
            return cpuUsage > 80.0f;
        }

        int GetOptimalThreadCount(float cpuUsage) {
            if (cpuUsage > 90.0f) return 1;
            if (cpuUsage > 70.0f) return 2;
            return 4;
        }
    }

    // PerformanceProfiler implementation
    std::mutex PerformanceProfiler::s_profileMutex;
    std::vector<std::pair<std::string, float>> PerformanceProfiler::s_profileResults;

    PerformanceProfiler::PerformanceProfiler(const std::string& name) : m_name(name) {}

    PerformanceProfiler::~PerformanceProfiler() {
        if (m_isRunning) {
            End();
        }
    }

    void PerformanceProfiler::Begin() {
        m_startTime = std::chrono::steady_clock::now();
        m_isRunning = true;
    }

    void PerformanceProfiler::End() {
        if (m_isRunning) {
            auto endTime = std::chrono::steady_clock::now();
            float elapsed = std::chrono::duration<float, std::milli>(endTime - m_startTime).count();
            ReportProfile(m_name, elapsed);
            m_isRunning = false;
        }
    }

    float PerformanceProfiler::GetElapsedTime() const {
        if (m_isRunning) {
            auto currentTime = std::chrono::steady_clock::now();
            return std::chrono::duration<float, std::milli>(currentTime - m_startTime).count();
        }
        return 0.0f;
    }

    void PerformanceProfiler::ReportProfile(const std::string& name, float time) {
        std::lock_guard<std::mutex> lock(s_profileMutex);
        s_profileResults.emplace_back(name, time);

        // Limit results to prevent memory growth
        if (s_profileResults.size() > 1000) {
            s_profileResults.erase(s_profileResults.begin(), s_profileResults.begin() + 500);
        }
    }

    std::vector<std::pair<std::string, float>> PerformanceProfiler::GetProfileResults() {
        std::lock_guard<std::mutex> lock(s_profileMutex);
        return s_profileResults;
    }

    void PerformanceProfiler::ClearProfiles() {
        std::lock_guard<std::mutex> lock(s_profileMutex);
        s_profileResults.clear();
    }

} // namespace ui
