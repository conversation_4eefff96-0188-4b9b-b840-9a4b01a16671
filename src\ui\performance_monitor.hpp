#pragma once

#include <chrono>
#include <vector>
#include <mutex>
#include <atomic>
#include <functional>

namespace ui {

    // Performance metrics structure
    struct PerformanceMetrics {
        float frameRate = 0.0f;
        float frameTime = 0.0f;
        float averageFrameRate = 0.0f;
        float minFrameRate = 0.0f;
        float maxFrameRate = 0.0f;
        
        size_t memoryUsage = 0;      // In bytes
        float cpuUsage = 0.0f;       // Percentage
        
        size_t drawCalls = 0;
        size_t triangles = 0;
        size_t vertices = 0;
        
        std::chrono::steady_clock::time_point timestamp;
        
        PerformanceMetrics() : timestamp(std::chrono::steady_clock::now()) {}
    };

    // Performance monitoring and optimization system
    class PerformanceMonitor {
    public:
        static PerformanceMonitor& GetInstance();
        
        // Frame rate monitoring
        void BeginFrame();
        void EndFrame();
        void UpdateFrameStats();
        
        // Memory monitoring
        void UpdateMemoryUsage();
        size_t GetCurrentMemoryUsage() const;
        
        // CPU monitoring
        void UpdateCPUUsage();
        float GetCurrentCPUUsage() const;
        
        // Rendering statistics
        void RecordDrawCall(size_t triangles = 0, size_t vertices = 0);
        void ResetRenderingStats();
        
        // Performance metrics
        const PerformanceMetrics& GetCurrentMetrics() const;
        std::vector<PerformanceMetrics> GetRecentMetrics(size_t count = 60) const;
        
        // Performance optimization
        bool ShouldOptimizeRendering() const;
        bool ShouldReduceAnimations() const;
        bool ShouldLimitUpdates() const;
        
        // Configuration
        void SetTargetFrameRate(float targetFPS);
        void SetPerformanceMode(bool highPerformance);
        void SetVSync(bool enabled);
        
        // Callbacks for performance events
        using PerformanceCallback = std::function<void(const PerformanceMetrics&)>;
        void RegisterPerformanceCallback(PerformanceCallback callback);
        
        // UI Integration
        void RenderPerformanceOverlay();
        void RenderPerformanceGraph();
        
    private:
        PerformanceMonitor() = default;
        ~PerformanceMonitor() = default;
        PerformanceMonitor(const PerformanceMonitor&) = delete;
        PerformanceMonitor& operator=(const PerformanceMonitor&) = delete;
        
        // Frame timing
        std::chrono::steady_clock::time_point m_frameStartTime;
        std::chrono::steady_clock::time_point m_lastFrameTime;
        std::vector<float> m_frameTimes;
        size_t m_frameTimeIndex = 0;
        static constexpr size_t MAX_FRAME_SAMPLES = 120;
        
        // Performance metrics
        mutable std::mutex m_metricsMutex;
        PerformanceMetrics m_currentMetrics;
        std::vector<PerformanceMetrics> m_metricsHistory;
        static constexpr size_t MAX_METRICS_HISTORY = 300; // 5 minutes at 60 FPS
        
        // Configuration
        float m_targetFrameRate = 60.0f;
        bool m_highPerformanceMode = false;
        bool m_vsyncEnabled = true;
        
        // Callbacks
        PerformanceCallback m_performanceCallback;
        
        // Internal methods
        void UpdateAverageFrameRate();
        void TrimMetricsHistory();
        float CalculateAverageFrameTime() const;
    };

    // Performance optimization utilities
    namespace PerformanceUtils {
        // Rendering optimizations
        bool ShouldSkipFrame(float targetFPS, float currentFPS);
        bool ShouldReduceQuality(const PerformanceMetrics& metrics);
        bool ShouldBatchDrawCalls(size_t currentDrawCalls);
        
        // Animation optimizations
        float GetOptimalAnimationSpeed(const PerformanceMetrics& metrics);
        bool ShouldDisableAnimations(const PerformanceMetrics& metrics);
        
        // Memory optimizations
        bool ShouldGarbageCollect(size_t currentMemory, size_t maxMemory);
        size_t GetOptimalCacheSize(const PerformanceMetrics& metrics);
        
        // CPU optimizations
        bool ShouldReduceUpdateFrequency(float cpuUsage);
        int GetOptimalThreadCount(float cpuUsage);
    }

    // Performance profiler for specific operations
    class PerformanceProfiler {
    public:
        explicit PerformanceProfiler(const std::string& name);
        ~PerformanceProfiler();
        
        void Begin();
        void End();
        float GetElapsedTime() const;
        
        static void ReportProfile(const std::string& name, float time);
        static std::vector<std::pair<std::string, float>> GetProfileResults();
        static void ClearProfiles();
        
    private:
        std::string m_name;
        std::chrono::steady_clock::time_point m_startTime;
        bool m_isRunning = false;
        
        static std::mutex s_profileMutex;
        static std::vector<std::pair<std::string, float>> s_profileResults;
    };

    // RAII performance profiler macro
    #define PROFILE_SCOPE(name) PerformanceProfiler _prof(name); _prof.Begin()
    #define PROFILE_FUNCTION() PROFILE_SCOPE(__FUNCTION__)

} // namespace ui
