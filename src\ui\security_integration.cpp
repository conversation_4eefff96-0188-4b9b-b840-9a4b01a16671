// Prevent Windows socket header conflicts
#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif
#ifndef NOMINMAX
#define NOMINMAX
#endif

#include "security_integration.hpp"
#include "theme.hpp"
#include <algorithm>
#include <sstream>
#include <iomanip>

// Forward declarations to avoid including security headers that might cause conflicts
namespace AntiDebugProtection {
    namespace AntiDebug {
        bool IsDebuggerAttached();
        bool CheckPEBFlags();
        bool CheckHardwareBreakpoints();
        bool VerifyCodeIntegrity();
    }
    namespace VMDetection {
        bool DetectVM();
    }
}

namespace SecurityCore {
    namespace AntiPatchProtection {
        bool VerifyFunctionIntegrity();
    }
}

namespace Security {
    namespace Config {
        bool IsDevelopmentMode();
    }
}

// Portable security function declarations
extern "C" {
    bool PORTABLE_HOOK_CHECK();
    bool PORTABLE_TIMING_CHECK();
    bool PORTABLE_DLL_INJECTION_CHECK();
}

// Prevent Windows macro conflicts
#ifdef min
#undef min
#endif
#ifdef max
#undef max
#endif

namespace ui {

    // SecurityIntegration Implementation
    SecurityIntegration& SecurityIntegration::GetInstance() {
        static SecurityIntegration instance;
        return instance;
    }

    void SecurityIntegration::Initialize() {
        if (m_initialized.exchange(true)) {
            return; // Already initialized
        }

        try {
            std::lock_guard<std::mutex> lock(m_mutex);

            // Register built-in security checks with safe wrappers
            RegisterSecurityCheck("Debugger Detection",
                "Monitors for attached debuggers and analysis tools",
                [this]() {
                    try { return RunDebuggerDetection(); }
                    catch (...) { return false; }
                });

            RegisterSecurityCheck("VM Detection",
                "Detects virtual machine environments",
                [this]() {
                    try { return RunVMDetection(); }
                    catch (...) { return false; }
                });

            RegisterSecurityCheck("Hook Detection",
                "Scans for suspicious API hooks and injections",
                [this]() {
                    try { return RunHookDetection(); }
                    catch (...) { return false; }
                });

            RegisterSecurityCheck("Integrity Check",
                "Verifies application and system integrity",
                [this]() {
                    try { return RunIntegrityCheck(); }
                    catch (...) { return false; }
                });

            RegisterSecurityCheck("Timing Analysis",
                "Detects timing-based attacks and analysis",
                [this]() {
                    try { return RunTimingCheck(); }
                    catch (...) { return false; }
                });

            RegisterSecurityCheck("DLL Injection Detection",
                "Monitors for DLL injection attempts",
                [this]() {
                    try { return RunDLLInjectionCheck(); }
                    catch (...) { return false; }
                });

            // Initialize all checks as unknown status
            for (const auto& [name, func] : m_checkFunctions) {
                m_checkResults[name].status = StatusIndicator::Status::Unknown;
                m_checkResults[name].lastRun = std::chrono::steady_clock::now() - std::chrono::hours(1); // Mark as old
            }

            m_lastFullCheck = std::chrono::steady_clock::now();
        } catch (...) {
            // If initialization fails, mark as not initialized
            m_initialized = false;
            throw;
        }
    }

    void SecurityIntegration::Shutdown() {
        if (!m_initialized.exchange(false)) {
            return; // Already shutdown
        }

        StopRealTimeMonitoring();
        
        // Wait for threads to finish
        if (m_monitoringThread.joinable()) {
            m_monitoringThread.join();
        }
        if (m_scheduledCheckThread.joinable()) {
            m_scheduledCheckThread.join();
        }

        std::lock_guard<std::mutex> lock(m_mutex);
        m_checkResults.clear();
        m_checkFunctions.clear();
        m_connectedDashboard = nullptr;
    }

    void SecurityIntegration::RegisterSecurityCheck(const std::string& name, const std::string& description, 
                                                   std::function<bool()> checkFunction) {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        m_checkFunctions[name] = checkFunction;
        m_checkResults[name] = SecurityCheckResult(name, description);
    }

    void SecurityIntegration::EnableSecurityCheck(const std::string& name, bool enabled) {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        auto it = m_checkResults.find(name);
        if (it != m_checkResults.end()) {
            it->second.isEnabled = enabled;
        }
    }

    void SecurityIntegration::RunSecurityCheck(const std::string& name) {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        auto funcIt = m_checkFunctions.find(name);
        auto resultIt = m_checkResults.find(name);
        
        if (funcIt == m_checkFunctions.end() || resultIt == m_checkResults.end()) {
            return;
        }

        if (!resultIt->second.isEnabled) {
            return;
        }

        // Fire check started event
        FireSecurityEvent(SecurityEvent(SecurityEventType::CheckStarted, name, 
                                      StatusIndicator::Status::Checking, "Security check started"));

        auto startTime = std::chrono::high_resolution_clock::now();
        
        try {
            bool threatDetected = funcIt->second();
            auto endTime = std::chrono::high_resolution_clock::now();
            
            float executionTime = std::chrono::duration<float, std::milli>(endTime - startTime).count();
            
            StatusIndicator::Status status;
            std::string details;
            
            if (threatDetected) {
                status = StatusIndicator::Status::Critical;
                details = "Security threat detected! Immediate attention required.";
                m_threatsDetected++;
                
                FireSecurityEvent(SecurityEvent(SecurityEventType::ThreatDetected, name, status, details));
                
                // Add critical notification
                SecurityNotificationSystem::GetInstance().AddNotification(
                    SecurityNotificationSystem::NotificationType::Critical,
                    "Security Threat Detected",
                    name + ": " + details,
                    0.0f // Don't auto-dismiss critical notifications
                );
            } else {
                status = StatusIndicator::Status::Secure;
                details = "No threats detected. System is secure.";
                
                FireSecurityEvent(SecurityEvent(SecurityEventType::CheckCompleted, name, status, details));
            }
            
            UpdateCheckResult(name, status, details, executionTime);
            m_totalChecksRun++;
            
        } catch (const std::exception& e) {
            auto endTime = std::chrono::high_resolution_clock::now();
            float executionTime = std::chrono::duration<float, std::milli>(endTime - startTime).count();
            
            std::string errorDetails = "Check failed with error: " + std::string(e.what());
            UpdateCheckResult(name, StatusIndicator::Status::Warning, errorDetails, executionTime);
            
            FireSecurityEvent(SecurityEvent(SecurityEventType::CheckCompleted, name, 
                                          StatusIndicator::Status::Warning, errorDetails));
        }
    }

    void SecurityIntegration::RunAllSecurityChecks() {
        for (const auto& [name, func] : m_checkFunctions) {
            RunSecurityCheck(name);
        }
        
        m_lastFullCheck = std::chrono::steady_clock::now();
        
        // Update connected dashboard
        if (m_connectedDashboard) {
            UpdateDashboardData();
        }
    }

    void SecurityIntegration::StartRealTimeMonitoring() {
        if (m_monitoringActive.exchange(true)) {
            return; // Already monitoring
        }

        m_shutdownRequested = false;
        
        // Start monitoring thread
        m_monitoringThread = std::thread(&SecurityIntegration::MonitoringThreadFunction, this);

        // Start scheduled check thread
        m_scheduledCheckThread = std::thread(&SecurityIntegration::ScheduledCheckThreadFunction, this);

        // Register action callback for UI triggers
        SecurityActionTrigger::GetInstance().RegisterActionCallback(
            [this](const SecurityActionTrigger::ActionRequest& request) {
                HandleSecurityAction(request);
            });

        SecurityNotificationSystem::GetInstance().AddNotification(
            SecurityNotificationSystem::NotificationType::Success,
            "Security Monitoring Started",
            "Real-time security monitoring is now active"
        );
    }

    void SecurityIntegration::StopRealTimeMonitoring() {
        if (!m_monitoringActive.exchange(false)) {
            return; // Already stopped
        }

        m_shutdownRequested = true;
        
        if (m_monitoringThread.joinable()) {
            m_monitoringThread.join();
        }
        if (m_scheduledCheckThread.joinable()) {
            m_scheduledCheckThread.join();
        }
        
        SecurityNotificationSystem::GetInstance().AddNotification(
            SecurityNotificationSystem::NotificationType::Info,
            "Security Monitoring Stopped",
            "Real-time security monitoring has been disabled"
        );
    }

    void SecurityIntegration::RegisterEventCallback(SecurityEventCallback callback) {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_eventCallback = callback;
    }

    void SecurityIntegration::RegisterUpdateCallback(SecurityUpdateCallback callback) {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_updateCallback = callback;
    }

    void SecurityIntegration::UnregisterEventCallback() {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_eventCallback = nullptr;
    }

    void SecurityIntegration::UnregisterUpdateCallback() {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_updateCallback = nullptr;
    }

    std::vector<SecurityCheckResult> SecurityIntegration::GetAllCheckResults() const {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        std::vector<SecurityCheckResult> results;
        for (const auto& [name, result] : m_checkResults) {
            results.push_back(result);
        }
        
        return results;
    }

    SecurityCheckResult SecurityIntegration::GetCheckResult(const std::string& name) const {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        auto it = m_checkResults.find(name);
        if (it != m_checkResults.end()) {
            return it->second;
        }
        
        return SecurityCheckResult("Unknown", "Check not found");
    }

    StatusIndicator::Status SecurityIntegration::GetOverallSecurityStatus() const {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        bool hasCritical = false;
        bool hasWarning = false;
        bool hasSecure = false;
        bool hasUnknown = false;
        
        for (const auto& [name, result] : m_checkResults) {
            if (!result.isEnabled) continue;
            
            switch (result.status) {
                case StatusIndicator::Status::Critical:
                    hasCritical = true;
                    break;
                case StatusIndicator::Status::Warning:
                    hasWarning = true;
                    break;
                case StatusIndicator::Status::Secure:
                    hasSecure = true;
                    break;
                case StatusIndicator::Status::Unknown:
                case StatusIndicator::Status::Checking:
                    hasUnknown = true;
                    break;
            }
        }
        
        // Priority: Critical > Warning > Unknown > Secure
        if (hasCritical) return StatusIndicator::Status::Critical;
        if (hasWarning) return StatusIndicator::Status::Warning;
        if (hasUnknown) return StatusIndicator::Status::Unknown;
        return StatusIndicator::Status::Secure;
    }

    float SecurityIntegration::GetSecurityScore() const {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        if (m_checkResults.empty()) return 0.0f;
        
        float totalScore = 0.0f;
        int enabledChecks = 0;
        
        for (const auto& [name, result] : m_checkResults) {
            if (!result.isEnabled) continue;
            
            enabledChecks++;
            switch (result.status) {
                case StatusIndicator::Status::Secure:
                    totalScore += 1.0f;
                    break;
                case StatusIndicator::Status::Warning:
                    totalScore += 0.5f;
                    break;
                case StatusIndicator::Status::Critical:
                    totalScore += 0.0f;
                    break;
                case StatusIndicator::Status::Unknown:
                case StatusIndicator::Status::Checking:
                    totalScore += 0.3f; // Partial credit for unknown
                    break;
            }
        }
        
        return enabledChecks > 0 ? totalScore / enabledChecks : 0.0f;
    }

    void SecurityIntegration::SetCheckInterval(std::chrono::milliseconds interval) {
        m_checkInterval = interval;
    }

    void SecurityIntegration::SetAutoRunEnabled(bool enabled) {
        m_autoRunEnabled = enabled;
    }

    void SecurityIntegration::ConnectToSecurityDashboard(SecurityDashboard* dashboard) {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_connectedDashboard = dashboard;
        
        if (dashboard) {
            UpdateDashboardData();
        }
    }

    void SecurityIntegration::UpdateDashboardData() {
        if (!m_connectedDashboard) return;
        
        std::vector<SecurityCheck> uiChecks;
        
        for (const auto& [name, result] : m_checkResults) {
            SecurityCheck uiCheck(name, result.description);
            uiCheck.status = result.status;
            uiCheck.details = result.details;
            uiCheck.isEnabled = result.isEnabled;
            uiChecks.push_back(uiCheck);
        }
        
        m_connectedDashboard->UpdateSecurityChecks(uiChecks);
    }

    // Internal security check implementations
    bool SecurityIntegration::RunDebuggerDetection() {
        if (Security::Config::IsDevelopmentMode()) {
            return false; // Skip in development mode
        }

        try {
            // Use the existing security system
            return AntiDebugProtection::AntiDebug::IsDebuggerAttached() ||
                   AntiDebugProtection::AntiDebug::CheckPEBFlags() ||
                   AntiDebugProtection::AntiDebug::CheckHardwareBreakpoints();
        } catch (...) {
            return false; // Assume safe if check fails
        }
    }

    bool SecurityIntegration::RunVMDetection() {
        if (Security::Config::IsDevelopmentMode()) {
            return false; // Skip in development mode
        }

        try {
            return AntiDebugProtection::VMDetection::DetectVM();
        } catch (...) {
            return false;
        }
    }

    bool SecurityIntegration::RunHookDetection() {
        if (Security::Config::IsDevelopmentMode()) {
            return false; // Skip in development mode
        }

        try {
            // Use portable security system
            return PORTABLE_HOOK_CHECK();
        } catch (...) {
            return false;
        }
    }

    bool SecurityIntegration::RunIntegrityCheck() {
        try {
            // Check code integrity
            bool codeIntegrity = AntiDebugProtection::AntiDebug::VerifyCodeIntegrity();

            // Check function integrity if available
            bool functionIntegrity = true;
            try {
                functionIntegrity = SecurityCore::AntiPatchProtection::VerifyFunctionIntegrity();
            } catch (...) {
                // Function integrity check not available or failed
            }

            return !(codeIntegrity && functionIntegrity);
        } catch (...) {
            return false;
        }
    }

    bool SecurityIntegration::RunTimingCheck() {
        if (Security::Config::IsDevelopmentMode()) {
            return false; // Skip in development mode
        }

        try {
            return PORTABLE_TIMING_CHECK();
        } catch (...) {
            return false;
        }
    }

    bool SecurityIntegration::RunDLLInjectionCheck() {
        if (Security::Config::IsDevelopmentMode()) {
            return false; // Skip in development mode
        }

        try {
            return PORTABLE_DLL_INJECTION_CHECK();
        } catch (...) {
            return false;
        }
    }

    void SecurityIntegration::MonitoringThreadFunction() {
        while (m_monitoringActive && !m_shutdownRequested) {
            try {
                // Run lightweight checks more frequently
                RunSecurityCheck("Debugger Detection");
                RunSecurityCheck("Hook Detection");

                // Update connected dashboard
                if (m_connectedDashboard) {
                    UpdateDashboardData();
                }

                // Sleep for a short interval
                std::this_thread::sleep_for(std::chrono::milliseconds(1000)); // 1 second

            } catch (...) {
                // Continue monitoring even if individual checks fail
            }
        }
    }

    void SecurityIntegration::ScheduledCheckThreadFunction() {
        while (m_monitoringActive && !m_shutdownRequested) {
            try {
                if (m_autoRunEnabled) {
                    // Run full security check suite at intervals
                    RunAllSecurityChecks();
                }

                // Sleep for the configured interval
                std::this_thread::sleep_for(m_checkInterval);

            } catch (...) {
                // Continue scheduling even if checks fail
            }
        }
    }

    void SecurityIntegration::FireSecurityEvent(const SecurityEvent& event) {
        std::lock_guard<std::mutex> lock(m_mutex);

        // Log the event
        SecurityEventLogger::GetInstance().LogEvent(event);

        if (m_eventCallback) {
            try {
                m_eventCallback(event);
            } catch (...) {
                // Don't let callback exceptions break the system
            }
        }
    }

    void SecurityIntegration::HandleSecurityAction(const SecurityActionTrigger::ActionRequest& request) {
        try {
            switch (request.action) {
                case SecurityActionType::RunFullScan:
                    {
                        SecurityNotificationSystem::GetInstance().AddProgressNotification(
                            "Full Security Scan", "Starting comprehensive security scan...", 0.0f);
                        RunAllSecurityChecks();
                        SecurityNotificationSystem::GetInstance().AddNotification(
                            SecurityNotificationSystem::NotificationType::Success,
                            "Full Scan Complete", "All security checks have been completed");
                    }
                    break;

                case SecurityActionType::RunSpecificCheck:
                    if (!request.target.empty()) {
                        SecurityNotificationSystem::GetInstance().AddProgressNotification(
                            "Security Check", "Running " + request.target + "...", 0.0f);
                        RunSecurityCheck(request.target);
                    }
                    break;

                case SecurityActionType::RestartMonitoring:
                    StopRealTimeMonitoring();
                    StartRealTimeMonitoring();
                    break;

                case SecurityActionType::ClearNotifications:
                    SecurityNotificationSystem::GetInstance().ClearReadNotifications();
                    break;

                case SecurityActionType::ExportSecurityLog:
                    {
                        std::string filename = request.parameters.count("filename") ?
                            request.parameters.at("filename") : "security_log.txt";
                        SecurityEventLogger::GetInstance().ExportLog(filename);
                        SecurityNotificationSystem::GetInstance().AddNotification(
                            SecurityNotificationSystem::NotificationType::Success,
                            "Log Exported", "Security log exported to " + filename);
                    }
                    break;

                case SecurityActionType::QuarantineThreat:
                case SecurityActionType::IgnoreThreat:
                    // These would be implemented based on specific threat handling requirements
                    SecurityNotificationSystem::GetInstance().AddNotification(
                        SecurityNotificationSystem::NotificationType::Info,
                        "Action Noted", "Threat action has been recorded");
                    break;

                case SecurityActionType::UpdateSecurityConfig:
                    // Configuration updates would be handled here
                    SecurityNotificationSystem::GetInstance().AddNotification(
                        SecurityNotificationSystem::NotificationType::Success,
                        "Configuration Updated", "Security configuration has been updated");
                    break;
            }
        } catch (...) {
            SecurityNotificationSystem::GetInstance().AddNotification(
                SecurityNotificationSystem::NotificationType::Critical,
                "Action Failed", "Security action could not be completed");
        }
    }

    void SecurityIntegration::UpdateCheckResult(const std::string& name, StatusIndicator::Status status,
                                               const std::string& details, float executionTime) {
        std::lock_guard<std::mutex> lock(m_mutex);

        auto it = m_checkResults.find(name);
        if (it != m_checkResults.end()) {
            it->second.status = status;
            it->second.details = details;
            it->second.executionTime = executionTime;
            it->second.lastRun = std::chrono::steady_clock::now();
        }

        // Fire update callback
        if (m_updateCallback) {
            try {
                std::vector<SecurityCheckResult> results;
                for (const auto& [checkName, result] : m_checkResults) {
                    results.push_back(result);
                }
                m_updateCallback(results);
            } catch (...) {
                // Don't let callback exceptions break the system
            }
        }
    }

    // SecurityActionTrigger Implementation
    SecurityActionTrigger& SecurityActionTrigger::GetInstance() {
        static SecurityActionTrigger instance;
        return instance;
    }

    void SecurityActionTrigger::RegisterActionCallback(ActionCallback callback) {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_actionCallback = callback;
    }

    void SecurityActionTrigger::TriggerAction(SecurityActionType action, const std::string& target,
                                            const std::map<std::string, std::string>& params) {
        std::lock_guard<std::mutex> lock(m_mutex);

        ActionRequest request(action, target);
        request.parameters = params;

        if (m_actionCallback) {
            try {
                m_actionCallback(request);

                // Log the action
                SecurityEventLogger::GetInstance().LogAction(request, "Action triggered successfully");
            } catch (...) {
                SecurityEventLogger::GetInstance().LogAction(request, "Action failed with exception");
            }
        }
    }

    // SecurityEventLogger Implementation
    SecurityEventLogger& SecurityEventLogger::GetInstance() {
        static SecurityEventLogger instance;
        return instance;
    }

    void SecurityEventLogger::LogEvent(const SecurityEvent& event, const std::string& additionalInfo) {
        std::lock_guard<std::mutex> lock(m_mutex);

        m_logEntries.emplace_back(event, additionalInfo);

        // Limit log size
        if (m_logEntries.size() > m_maxLogSize) {
            m_logEntries.erase(m_logEntries.begin());
        }
    }

    void SecurityEventLogger::LogAction(const SecurityActionTrigger::ActionRequest& action, const std::string& result) {
        std::lock_guard<std::mutex> lock(m_mutex);

        // Create a security event for the action
        SecurityEvent actionEvent(SecurityEventType::ConfigurationChanged, action.target,
                                StatusIndicator::Status::Unknown, "User action: " + std::to_string(static_cast<int>(action.action)));

        m_logEntries.emplace_back(actionEvent, result);

        // Limit log size
        if (m_logEntries.size() > m_maxLogSize) {
            m_logEntries.erase(m_logEntries.begin());
        }
    }

    std::vector<SecurityEventLogger::LogEntry> SecurityEventLogger::GetRecentEvents(size_t maxCount) const {
        std::lock_guard<std::mutex> lock(m_mutex);

        std::vector<LogEntry> recent;
        size_t startIndex = m_logEntries.size() > maxCount ? m_logEntries.size() - maxCount : 0;

        for (size_t i = startIndex; i < m_logEntries.size(); ++i) {
            recent.push_back(m_logEntries[i]);
        }

        return recent;
    }

    std::vector<SecurityEventLogger::LogEntry> SecurityEventLogger::GetEventsByType(SecurityEventType type, size_t maxCount) const {
        std::lock_guard<std::mutex> lock(m_mutex);

        std::vector<LogEntry> filtered;

        for (auto it = m_logEntries.rbegin(); it != m_logEntries.rend() && filtered.size() < maxCount; ++it) {
            if (it->event.type == type) {
                filtered.push_back(*it);
            }
        }

        return filtered;
    }

    void SecurityEventLogger::ClearLog() {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_logEntries.clear();
    }

    void SecurityEventLogger::ExportLog(const std::string& filename) const {
        std::lock_guard<std::mutex> lock(m_mutex);

        try {
            std::ofstream file(filename);
            if (!file.is_open()) return;

            file << "Security Event Log Export\n";
            file << "Generated: " << std::chrono::duration_cast<std::chrono::seconds>(
                std::chrono::steady_clock::now().time_since_epoch()).count() << "\n\n";

            for (const auto& entry : m_logEntries) {
                file << "Event: " << entry.event.checkName << "\n";
                file << "Type: " << static_cast<int>(entry.event.type) << "\n";
                file << "Status: " << static_cast<int>(entry.event.status) << "\n";
                file << "Message: " << entry.event.message << "\n";
                if (!entry.additionalInfo.empty()) {
                    file << "Additional Info: " << entry.additionalInfo << "\n";
                }
                file << "---\n";
            }
        } catch (...) {
            // Ignore export errors
        }
    }

    // Enhanced SecurityNotificationSystem Implementation
    SecurityNotificationSystem& SecurityNotificationSystem::GetInstance() {
        static SecurityNotificationSystem instance;
        return instance;
    }

    void SecurityNotificationSystem::AddNotification(NotificationType type, const std::string& title,
                                                    const std::string& message, float displayTime) {
        std::lock_guard<std::mutex> lock(m_mutex);

        m_notifications.emplace_back(type, title, message, displayTime);

        // Limit the number of stored notifications
        if (m_notifications.size() > m_maxNotifications) {
            m_notifications.erase(m_notifications.begin());
        }
    }

    void SecurityNotificationSystem::AddActionNotification(const std::string& title, const std::string& message,
                                                          const std::vector<NotificationAction>& actions, float displayTime) {
        std::lock_guard<std::mutex> lock(m_mutex);

        Notification notification(NotificationType::Action, title, message, displayTime);
        notification.actions = actions;
        m_notifications.push_back(notification);

        // Limit the number of stored notifications
        if (m_notifications.size() > m_maxNotifications) {
            m_notifications.erase(m_notifications.begin());
        }
    }

    void SecurityNotificationSystem::AddProgressNotification(const std::string& title, const std::string& message,
                                                            float progress, float displayTime) {
        std::lock_guard<std::mutex> lock(m_mutex);

        Notification notification(NotificationType::Progress, title, message, displayTime);
        notification.progress = std::clamp(progress, 0.0f, 1.0f);
        m_notifications.push_back(notification);

        // Limit the number of stored notifications
        if (m_notifications.size() > m_maxNotifications) {
            m_notifications.erase(m_notifications.begin());
        }
    }

    void SecurityNotificationSystem::UpdateProgressNotification(size_t index, float progress, const std::string& message) {
        std::lock_guard<std::mutex> lock(m_mutex);

        if (index < m_notifications.size() && m_notifications[index].type == NotificationType::Progress) {
            m_notifications[index].progress = std::clamp(progress, 0.0f, 1.0f);
            if (!message.empty()) {
                m_notifications[index].message = message;
            }
        }
    }

    void SecurityNotificationSystem::MarkAsRead(size_t index) {
        std::lock_guard<std::mutex> lock(m_mutex);

        if (index < m_notifications.size()) {
            m_notifications[index].isRead = true;
        }
    }

    void SecurityNotificationSystem::ClearAllNotifications() {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_notifications.clear();
    }

    void SecurityNotificationSystem::ClearReadNotifications() {
        std::lock_guard<std::mutex> lock(m_mutex);

        m_notifications.erase(
            std::remove_if(m_notifications.begin(), m_notifications.end(),
                          [](const Notification& n) { return n.isRead; }),
            m_notifications.end()
        );
    }

    void SecurityNotificationSystem::ExecuteNotificationAction(size_t notificationIndex, size_t actionIndex) {
        std::lock_guard<std::mutex> lock(m_mutex);

        if (notificationIndex < m_notifications.size()) {
            auto& notification = m_notifications[notificationIndex];
            if (actionIndex < notification.actions.size()) {
                auto& action = notification.actions[actionIndex];

                // Trigger the action
                SecurityActionTrigger::GetInstance().TriggerAction(action.action, action.target, action.parameters);

                // Mark notification as read after action
                notification.isRead = true;
            }
        }
    }

    std::vector<SecurityNotificationSystem::Notification> SecurityNotificationSystem::GetActiveNotifications() const {
        std::lock_guard<std::mutex> lock(m_mutex);

        std::vector<Notification> active;
        auto now = std::chrono::steady_clock::now();

        for (const auto& notification : m_notifications) {
            if (!notification.isRead) {
                if (notification.displayTime <= 0.0f) {
                    // Persistent notification
                    active.push_back(notification);
                } else {
                    // Check if still within display time
                    auto elapsed = std::chrono::duration<float>(now - notification.timestamp).count();
                    if (elapsed < notification.displayTime) {
                        active.push_back(notification);
                    }
                }
            }
        }

        return active;
    }

    std::vector<SecurityNotificationSystem::Notification> SecurityNotificationSystem::GetAllNotifications() const {
        std::lock_guard<std::mutex> lock(m_mutex);
        return m_notifications;
    }

    size_t SecurityNotificationSystem::GetUnreadCount() const {
        std::lock_guard<std::mutex> lock(m_mutex);

        return std::count_if(m_notifications.begin(), m_notifications.end(),
                           [](const Notification& n) { return !n.isRead; });
    }

    size_t SecurityNotificationSystem::GetCriticalCount() const {
        std::lock_guard<std::mutex> lock(m_mutex);

        return std::count_if(m_notifications.begin(), m_notifications.end(),
                           [](const Notification& n) { return !n.isRead && n.type == NotificationType::Critical; });
    }

    void SecurityNotificationSystem::RenderNotificationPopups() {
        auto activeNotifications = GetActiveNotifications();

        for (size_t i = 0; i < activeNotifications.size(); ++i) {
            const auto& notification = activeNotifications[i];

            // Set popup position (stack them vertically)
            ImVec2 popupPos(ImGui::GetIO().DisplaySize.x - 320, 50 + static_cast<float>(i) * 120);
            ImGui::SetNextWindowPos(popupPos, ImGuiCond_Always);
            ImGui::SetNextWindowSize(ImVec2(300, 100), ImGuiCond_Always);

            // Choose colors based on notification type
            ImVec4 bgColor, borderColor;
            const auto& theme = Theme::GetCurrentTheme();

            switch (notification.type) {
                case NotificationType::Critical:
                    bgColor = theme.danger;
                    borderColor = theme.danger;
                    break;
                case NotificationType::Warning:
                    bgColor = theme.warning;
                    borderColor = theme.warning;
                    break;
                case NotificationType::Success:
                    bgColor = theme.success;
                    borderColor = theme.success;
                    break;
                default:
                    bgColor = theme.primary;
                    borderColor = theme.primary;
                    break;
            }

            ImGui::PushStyleColor(ImGuiCol_WindowBg, Theme::AdjustAlpha(bgColor, 0.9f));
            ImGui::PushStyleColor(ImGuiCol_Border, borderColor);
            ImGui::PushStyleVar(ImGuiStyleVar_WindowBorderSize, 2.0f);

            std::string popupId = "SecurityNotification##" + std::to_string(i);
            if (ImGui::Begin(popupId.c_str(), nullptr,
                           ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoResize |
                           ImGuiWindowFlags_NoMove | ImGuiWindowFlags_NoScrollbar)) {

                ImGui::TextWrapped("%s", notification.title.c_str());
                ImGui::Separator();
                ImGui::TextWrapped("%s", notification.message.c_str());

                // Auto-dismiss button for persistent notifications
                if (notification.displayTime <= 0.0f) {
                    if (ImGui::Button("Dismiss")) {
                        // Find and mark as read
                        std::lock_guard<std::mutex> lock(m_mutex);
                        for (size_t j = 0; j < m_notifications.size(); ++j) {
                            if (m_notifications[j].timestamp == notification.timestamp) {
                                m_notifications[j].isRead = true;
                                break;
                            }
                        }
                    }
                }
            }
            ImGui::End();

            ImGui::PopStyleVar();
            ImGui::PopStyleColor(2);
        }
    }

    void SecurityNotificationSystem::RenderNotificationList() {
        if (ImGui::CollapsingHeader("Security Notifications")) {
            auto allNotifications = GetAllNotifications();

            if (allNotifications.empty()) {
                ImGui::TextDisabled("No notifications");
                return;
            }

            // Show unread count
            size_t unreadCount = GetUnreadCount();
            if (unreadCount > 0) {
                ImGui::Text("Unread: %zu", unreadCount);
                ImGui::SameLine();
                if (ImGui::Button("Mark All Read")) {
                    std::lock_guard<std::mutex> lock(m_mutex);
                    for (auto& notification : m_notifications) {
                        notification.isRead = true;
                    }
                }
                ImGui::SameLine();
            }

            if (ImGui::Button("Clear All")) {
                ClearAllNotifications();
            }

            ImGui::Separator();

            // Render notification list
            for (size_t i = 0; i < allNotifications.size(); ++i) {
                const auto& notification = allNotifications[i];

                ImGui::PushID(static_cast<int>(i));

                // Color code by type and read status
                ImVec4 textColor = Theme::GetCurrentTheme().text;
                if (notification.isRead) {
                    textColor = Theme::GetCurrentTheme().textSecondary;
                }

                ImGui::PushStyleColor(ImGuiCol_Text, textColor);

                // Show notification icon
                const char* icon = "ℹ";
                switch (notification.type) {
                    case NotificationType::Critical: icon = "⚠"; break;
                    case NotificationType::Warning: icon = "⚠"; break;
                    case NotificationType::Success: icon = "✓"; break;
                    default: icon = "ℹ"; break;
                }

                ImGui::Text("%s %s", icon, notification.title.c_str());
                ImGui::TextWrapped("   %s", notification.message.c_str());

                // Show timestamp
                auto now = std::chrono::steady_clock::now();
                auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - notification.timestamp).count();
                ImGui::TextDisabled("   %lld seconds ago", elapsed);

                if (!notification.isRead && ImGui::IsItemClicked()) {
                    MarkAsRead(i);
                }

                ImGui::PopStyleColor();
                ImGui::PopID();

                if (i < allNotifications.size() - 1) {
                    ImGui::Separator();
                }
            }
        }
    }

    // SecurityConfigUI Implementation
    void SecurityConfigUI::RenderSecuritySettings() {
        if (ImGui::CollapsingHeader("Security Configuration")) {
            SecurityIntegration& security = SecurityIntegration::GetInstance();

            // Auto-run settings
            bool autoRun = security.IsAutoRunEnabled();
            if (ImGui::Checkbox("Enable automatic security checks", &autoRun)) {
                security.SetAutoRunEnabled(autoRun);
            }

            // Monitoring settings
            bool monitoring = security.IsMonitoringActive();
            if (ImGui::Checkbox("Enable real-time monitoring", &monitoring)) {
                if (monitoring) {
                    security.StartRealTimeMonitoring();
                } else {
                    security.StopRealTimeMonitoring();
                }
            }

            ImGui::Separator();

            // Individual check configuration
            ImGui::Text("Security Checks:");
            auto checkResults = security.GetAllCheckResults();

            for (auto& result : checkResults) {
                RenderCheckConfiguration(result.name, result);
            }

            ImGui::Separator();

            // Manual actions
            if (ImGui::Button("Run All Checks Now")) {
                security.RunAllSecurityChecks();
            }

            ImGui::SameLine();
            if (ImGui::Button("Clear Notifications")) {
                SecurityNotificationSystem::GetInstance().ClearReadNotifications();
            }
        }
    }

    void SecurityConfigUI::RenderCheckConfiguration(const std::string& checkName, SecurityCheckResult& result) {
        ImGui::PushID(checkName.c_str());

        // Enable/disable checkbox
        bool enabled = result.isEnabled;
        if (ImGui::Checkbox("##enabled", &enabled)) {
            SecurityIntegration::GetInstance().EnableSecurityCheck(checkName, enabled);
        }

        ImGui::SameLine();

        // Status indicator
        const char* statusIcon = "?";
        ImVec4 statusColor = Theme::GetCurrentTheme().textSecondary;

        switch (result.status) {
            case StatusIndicator::Status::Secure:
                statusIcon = "✓";
                statusColor = Theme::GetCurrentTheme().success;
                break;
            case StatusIndicator::Status::Warning:
                statusIcon = "⚠";
                statusColor = Theme::GetCurrentTheme().warning;
                break;
            case StatusIndicator::Status::Critical:
                statusIcon = "⚠";
                statusColor = Theme::GetCurrentTheme().danger;
                break;
            case StatusIndicator::Status::Checking:
                statusIcon = "⟳";
                statusColor = Theme::GetCurrentTheme().primary;
                break;
        }

        ImGui::TextColored(statusColor, "%s", statusIcon);
        ImGui::SameLine();

        // Check name and run button
        ImGui::Text("%s", checkName.c_str());
        ImGui::SameLine();

        if (ImGui::Button(("Run##" + checkName).c_str())) {
            SecurityIntegration::GetInstance().RunSecurityCheck(checkName);
        }

        // Show details on hover
        if (ImGui::IsItemHovered() && !result.details.empty()) {
            ImGui::SetTooltip("%s\nLast run: %.2fms", result.details.c_str(), result.executionTime);
        }

        ImGui::PopID();
    }

    void SecurityConfigUI::RenderSecurityStatus() {
        if (ImGui::CollapsingHeader("Security Status Overview")) {
            SecurityIntegration& security = SecurityIntegration::GetInstance();

            // Overall status
            StatusIndicator::Status overallStatus = security.GetOverallSecurityStatus();
            float securityScore = security.GetSecurityScore();

            ImGui::Text("Overall Security Status:");
            ImGui::SameLine();

            const char* statusText = "Unknown";
            ImVec4 statusColor = Theme::GetCurrentTheme().textSecondary;

            switch (overallStatus) {
                case StatusIndicator::Status::Secure:
                    statusText = "SECURE";
                    statusColor = Theme::GetCurrentTheme().success;
                    break;
                case StatusIndicator::Status::Warning:
                    statusText = "WARNING";
                    statusColor = Theme::GetCurrentTheme().warning;
                    break;
                case StatusIndicator::Status::Critical:
                    statusText = "CRITICAL";
                    statusColor = Theme::GetCurrentTheme().danger;
                    break;
            }

            ImGui::TextColored(statusColor, "%s", statusText);

            // Security score
            ImGui::Text("Security Score: %.1f%%", securityScore * 100.0f);
            ImGui::ProgressBar(securityScore, ImVec2(-1, 0));

            // Quick stats
            auto checkResults = security.GetAllCheckResults();
            int secureCount = 0, warningCount = 0, criticalCount = 0;

            for (const auto& result : checkResults) {
                if (!result.isEnabled) continue;
                switch (result.status) {
                    case StatusIndicator::Status::Secure: secureCount++; break;
                    case StatusIndicator::Status::Warning: warningCount++; break;
                    case StatusIndicator::Status::Critical: criticalCount++; break;
                }
            }

            ImGui::Text("Checks: %d Secure, %d Warnings, %d Critical", secureCount, warningCount, criticalCount);
        }
    }

    void SecurityConfigUI::RenderThreatLog() {
        if (ImGui::CollapsingHeader("Threat Detection Log")) {
            ImGui::TextDisabled("Threat logging functionality would be implemented here");
            ImGui::Text("This would show a history of detected threats and security events");
        }
    }

    void SecurityConfigUI::RenderSecurityMetrics() {
        if (ImGui::CollapsingHeader("Security Metrics")) {
            ImGui::TextDisabled("Security metrics would be displayed here");
            ImGui::Text("This would show performance metrics, check frequencies, etc.");
        }
    }

    void SecurityConfigUI::RenderSecurityChart() {
        // Placeholder for security chart rendering
        ImGui::TextDisabled("Security chart visualization would be implemented here");
    }

    void SecurityConfigUI::RenderThreatHistory() {
        // Placeholder for threat history rendering
        ImGui::TextDisabled("Threat history visualization would be implemented here");
    }

} // namespace ui
