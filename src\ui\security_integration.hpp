#pragma once

// Prevent Windows socket header conflicts
#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif
#ifndef NOMINMAX
#define NOMINMAX
#endif

#include "components.hpp"
#include <functional>
#include <thread>
#include <atomic>
#include <mutex>
#include <chrono>

// Prevent Windows macro conflicts
#ifdef min
#undef min
#endif
#ifdef max
#undef max
#endif

namespace ui {

    // Security event types
    enum class SecurityEventType {
        CheckStarted,
        CheckCompleted,
        ThreatDetected,
        ThreatResolved,
        SystemStatusChanged,
        ConfigurationChanged
    };

    // Security event data
    struct SecurityEvent {
        SecurityEventType type;
        std::string checkName;
        StatusIndicator::Status status;
        std::string message;
        std::chrono::steady_clock::time_point timestamp;
        
        SecurityEvent(SecurityEventType t, const std::string& name, StatusIndicator::Status s, const std::string& msg)
            : type(t), checkName(name), status(s), message(msg), timestamp(std::chrono::steady_clock::now()) {}
    };

    // Security check result
    struct SecurityCheckResult {
        std::string name;
        std::string description;
        StatusIndicator::Status status;
        std::string details;
        float executionTime;
        std::chrono::steady_clock::time_point lastRun;
        bool isEnabled;

        // Default constructor for std::map
        SecurityCheckResult()
            : name(""), description(""), status(StatusIndicator::Status::Unknown),
              executionTime(0.0f), lastRun(std::chrono::steady_clock::now()), isEnabled(true) {}

        SecurityCheckResult(const std::string& n, const std::string& desc)
            : name(n), description(desc), status(StatusIndicator::Status::Unknown),
              executionTime(0.0f), lastRun(std::chrono::steady_clock::now()), isEnabled(true) {}
    };

    // Security system integration manager
    class SecurityIntegration {
    public:
        using SecurityEventCallback = std::function<void(const SecurityEvent&)>;
        using SecurityUpdateCallback = std::function<void(const std::vector<SecurityCheckResult>&)>;

        static SecurityIntegration& GetInstance();
        
        // Initialization and cleanup
        void Initialize();
        void Shutdown();
        
        // Security check management
        void RegisterSecurityCheck(const std::string& name, const std::string& description, 
                                 std::function<bool()> checkFunction);
        void EnableSecurityCheck(const std::string& name, bool enabled);
        void RunSecurityCheck(const std::string& name);
        void RunAllSecurityChecks();
        
        // Real-time monitoring
        void StartRealTimeMonitoring();
        void StopRealTimeMonitoring();
        bool IsMonitoringActive() const { return m_monitoringActive; }
        
        // Event handling
        void RegisterEventCallback(SecurityEventCallback callback);
        void RegisterUpdateCallback(SecurityUpdateCallback callback);
        void UnregisterEventCallback();
        void UnregisterUpdateCallback();
        
        // Status queries
        std::vector<SecurityCheckResult> GetAllCheckResults() const;
        SecurityCheckResult GetCheckResult(const std::string& name) const;
        StatusIndicator::Status GetOverallSecurityStatus() const;
        float GetSecurityScore() const; // 0.0 to 1.0
        
        // Configuration
        void SetCheckInterval(std::chrono::milliseconds interval);
        void SetAutoRunEnabled(bool enabled);
        bool IsAutoRunEnabled() const { return m_autoRunEnabled; }
        
        // UI Integration helpers
        void ConnectToSecurityDashboard(SecurityDashboard* dashboard);
        void UpdateDashboardData();
        
    private:
        SecurityIntegration() = default;
        ~SecurityIntegration() = default;
        SecurityIntegration(const SecurityIntegration&) = delete;
        SecurityIntegration& operator=(const SecurityIntegration&) = delete;
        
        // Internal security check functions
        bool RunDebuggerDetection();
        bool RunVMDetection();
        bool RunHookDetection();
        bool RunIntegrityCheck();
        bool RunTimingCheck();
        bool RunDLLInjectionCheck();
        
        // Threading and monitoring
        void MonitoringThreadFunction();
        void ScheduledCheckThreadFunction();
        void FireSecurityEvent(const SecurityEvent& event);
        void UpdateCheckResult(const std::string& name, StatusIndicator::Status status,
                             const std::string& details, float executionTime);

        // Security action handling (forward declaration resolved in implementation)
        void HandleSecurityAction(const void* request);
        
        // Data members
        mutable std::mutex m_mutex;
        std::map<std::string, SecurityCheckResult> m_checkResults;
        std::map<std::string, std::function<bool()>> m_checkFunctions;
        
        SecurityEventCallback m_eventCallback;
        SecurityUpdateCallback m_updateCallback;
        SecurityDashboard* m_connectedDashboard = nullptr;
        
        std::atomic<bool> m_initialized{false};
        std::atomic<bool> m_monitoringActive{false};
        std::atomic<bool> m_autoRunEnabled{true};
        std::atomic<bool> m_shutdownRequested{false};
        
        std::thread m_monitoringThread;
        std::thread m_scheduledCheckThread;
        
        std::chrono::milliseconds m_checkInterval{5000}; // 5 seconds default
        std::chrono::steady_clock::time_point m_lastFullCheck;
        
        // Statistics
        std::atomic<uint32_t> m_totalChecksRun{0};
        std::atomic<uint32_t> m_threatsDetected{0};
        std::atomic<uint32_t> m_falsePositives{0};
    };

    // Security action types for UI triggers
    enum class SecurityActionType {
        RunFullScan,
        RunSpecificCheck,
        QuarantineThreat,
        IgnoreThreat,
        RestartMonitoring,
        UpdateSecurityConfig,
        ExportSecurityLog,
        ClearNotifications
    };

    // Security action trigger system
    class SecurityActionTrigger {
    public:
        struct ActionRequest {
            SecurityActionType action;
            std::string target; // Check name, threat ID, etc.
            std::map<std::string, std::string> parameters;
            std::chrono::steady_clock::time_point timestamp;

            ActionRequest(SecurityActionType a, const std::string& t = "")
                : action(a), target(t), timestamp(std::chrono::steady_clock::now()) {}
        };

        using ActionCallback = std::function<void(const ActionRequest&)>;

        static SecurityActionTrigger& GetInstance();

        void RegisterActionCallback(ActionCallback callback);
        void TriggerAction(SecurityActionType action, const std::string& target = "",
                          const std::map<std::string, std::string>& params = {});

    private:
        SecurityActionTrigger() = default;
        ActionCallback m_actionCallback;
        std::mutex m_mutex;
    };

    // Security event logger for audit trail
    class SecurityEventLogger {
    public:
        struct LogEntry {
            SecurityEvent event;
            std::string additionalInfo;
            std::chrono::steady_clock::time_point logTime;

            LogEntry(const SecurityEvent& e, const std::string& info = "")
                : event(e), additionalInfo(info), logTime(std::chrono::steady_clock::now()) {}
        };

        static SecurityEventLogger& GetInstance();

        void LogEvent(const SecurityEvent& event, const std::string& additionalInfo = "");
        void LogAction(const SecurityActionTrigger::ActionRequest& action, const std::string& result);

        std::vector<LogEntry> GetRecentEvents(size_t maxCount = 100) const;
        std::vector<LogEntry> GetEventsByType(SecurityEventType type, size_t maxCount = 50) const;
        void ClearLog();
        void ExportLog(const std::string& filename) const;

    private:
        SecurityEventLogger() = default;
        mutable std::mutex m_mutex;
        std::vector<LogEntry> m_logEntries;
        size_t m_maxLogSize = 1000;
    };

    // Enhanced security notification system
    class SecurityNotificationSystem {
    public:
        enum class NotificationType {
            Info,
            Warning,
            Critical,
            Success,
            Action,      // Notifications with action buttons
            Progress     // Progress notifications for long operations
        };

        struct NotificationAction {
            std::string label;
            SecurityActionType action;
            std::string target;
            std::map<std::string, std::string> parameters;

            NotificationAction(const std::string& l, SecurityActionType a, const std::string& t = "")
                : label(l), action(a), target(t) {}
        };

        struct Notification {
            NotificationType type;
            std::string title;
            std::string message;
            std::chrono::steady_clock::time_point timestamp;
            bool isRead;
            float displayTime; // How long to show (0 = until dismissed)
            std::vector<NotificationAction> actions; // Action buttons
            float progress; // For progress notifications (0.0 - 1.0)

            Notification(NotificationType t, const std::string& title, const std::string& msg, float time = 5.0f)
                : type(t), title(title), message(msg), timestamp(std::chrono::steady_clock::now()),
                  isRead(false), displayTime(time), progress(0.0f) {}
        };
        
        static SecurityNotificationSystem& GetInstance();

        void AddNotification(NotificationType type, const std::string& title, const std::string& message, float displayTime = 5.0f);
        void AddActionNotification(const std::string& title, const std::string& message,
                                 const std::vector<NotificationAction>& actions, float displayTime = 0.0f);
        void AddProgressNotification(const std::string& title, const std::string& message,
                                   float progress, float displayTime = 0.0f);
        void UpdateProgressNotification(size_t index, float progress, const std::string& message = "");

        void MarkAsRead(size_t index);
        void ClearAllNotifications();
        void ClearReadNotifications();
        void ExecuteNotificationAction(size_t notificationIndex, size_t actionIndex);

        std::vector<Notification> GetActiveNotifications() const;
        std::vector<Notification> GetAllNotifications() const;
        size_t GetUnreadCount() const;
        size_t GetCriticalCount() const;

        // UI Integration
        void RenderNotificationPopups();
        void RenderNotificationList();
        void RenderNotificationCenter(); // Enhanced notification center UI
        
    private:
        SecurityNotificationSystem() = default;
        ~SecurityNotificationSystem() = default;
        
        mutable std::mutex m_mutex;
        std::vector<Notification> m_notifications;
        size_t m_maxNotifications = 100;
    };

    // Security configuration UI helper
    class SecurityConfigUI {
    public:
        static void RenderSecuritySettings();
        static void RenderSecurityStatus();
        static void RenderThreatLog();
        static void RenderSecurityMetrics();
        
    private:
        static void RenderCheckConfiguration(const std::string& checkName, SecurityCheckResult& result);
        static void RenderSecurityChart();
        static void RenderThreatHistory();
    };

} // namespace ui
