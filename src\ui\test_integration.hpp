#pragma once

// Simple test integration header
// This file provides basic test functionality without requiring separate compilation

#ifdef ENABLE_UI_TESTS

#include <iostream>
#include <string>
#include "animation.hpp"
#include "theme.hpp"
#include "performance_monitor.hpp"
#include "accessibility.hpp"
#include "security_integration.hpp"

namespace ui {
namespace test {

    // Simple test assertion macro
    #define UI_TEST_ASSERT(condition, message) \
        if (!(condition)) { \
            std::cerr << "TEST FAILED: " << message << std::endl; \
            return false; \
        }

    // Basic test runner for core functionality
    inline bool RunBasicTests() {
        std::cout << "Running basic UI system tests..." << std::endl;
        
        bool allPassed = true;
        
        // Test 1: Animation system basic functionality
        try {
            float testValue = Animation::Transitions::SpinnerAnimation(1.0f, 2.0f);
            UI_TEST_ASSERT(testValue >= 0.0f, "Spinner animation should return valid value");
            std::cout << "✓ Animation system test passed" << std::endl;
        } catch (...) {
            std::cout << "✗ Animation system test failed" << std::endl;
            allPassed = false;
        }
        
        // Test 2: Theme system
        try {
            const auto& theme = Theme::GetCurrentTheme();
            UI_TEST_ASSERT(theme.primary.w > 0.0f, "Theme should have valid alpha values");
            std::cout << "✓ Theme system test passed" << std::endl;
        } catch (...) {
            std::cout << "✗ Theme system test failed" << std::endl;
            allPassed = false;
        }
        
        // Test 3: Performance monitor
        try {
            PerformanceMonitor& monitor = PerformanceMonitor::GetInstance();
            monitor.BeginFrame();
            monitor.EndFrame();
            const auto& metrics = monitor.GetCurrentMetrics();
            UI_TEST_ASSERT(metrics.frameTime >= 0.0f, "Performance metrics should be valid");
            std::cout << "✓ Performance monitor test passed" << std::endl;
        } catch (...) {
            std::cout << "✗ Performance monitor test failed" << std::endl;
            allPassed = false;
        }
        
        // Test 4: Accessibility system
        try {
            AccessibilitySystem& accessibility = AccessibilitySystem::GetInstance();
            accessibility.Initialize();
            const auto& config = accessibility.GetConfig();
            UI_TEST_ASSERT(config.textScale > 0.0f, "Accessibility config should be valid");
            std::cout << "✓ Accessibility system test passed" << std::endl;
        } catch (...) {
            std::cout << "✗ Accessibility system test failed" << std::endl;
            allPassed = false;
        }
        
        // Test 5: Security integration
        try {
            SecurityIntegration& security = SecurityIntegration::GetInstance();
            // Basic initialization test
            std::cout << "✓ Security integration test passed" << std::endl;
        } catch (...) {
            std::cout << "✗ Security integration test failed" << std::endl;
            allPassed = false;
        }
        
        if (allPassed) {
            std::cout << "\n✅ All basic UI tests passed!" << std::endl;
        } else {
            std::cout << "\n❌ Some basic UI tests failed!" << std::endl;
        }
        
        return allPassed;
    }
    
    // Test runner that can be called from main application
    inline void RunTestsIfEnabled() {
        #ifdef ENABLE_UI_TESTS
        std::cout << "=== UI System Tests ===" << std::endl;
        RunBasicTests();
        std::cout << "======================" << std::endl;
        #endif
    }

} // namespace test
} // namespace ui

#else

namespace ui {
namespace test {
    // No-op when tests are disabled
    inline void RunTestsIfEnabled() {}
}
}

#endif // ENABLE_UI_TESTS
