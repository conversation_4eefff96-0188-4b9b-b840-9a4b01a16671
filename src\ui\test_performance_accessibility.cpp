#include "performance_monitor.hpp"
#include "accessibility.hpp"
#include <iostream>
#include <cassert>
#include <thread>
#include <chrono>

// Simple test framework
#define TEST_ASSERT(condition, message) \
    if (!(condition)) { \
        std::cerr << "TEST FAILED: " << message << std::endl; \
        return false; \
    }

namespace ui {
namespace test {

    bool TestPerformanceMonitor() {
        std::cout << "Testing PerformanceMonitor..." << std::endl;
        
        PerformanceMonitor& monitor = PerformanceMonitor::GetInstance();
        
        // Test basic frame monitoring
        monitor.BeginFrame();
        std::this_thread::sleep_for(std::chrono::milliseconds(16)); // Simulate 60 FPS frame
        monitor.EndFrame();
        
        const auto& metrics = monitor.GetCurrentMetrics();
        TEST_ASSERT(metrics.frameTime > 0.0f, "Frame time should be recorded");
        TEST_ASSERT(metrics.frameRate > 0.0f, "Frame rate should be calculated");
        
        // Test performance optimization suggestions
        monitor.SetTargetFrameRate(60.0f);
        
        // Simulate low performance
        for (int i = 0; i < 10; ++i) {
            monitor.BeginFrame();
            std::this_thread::sleep_for(std::chrono::milliseconds(50)); // Simulate 20 FPS
            monitor.EndFrame();
        }
        
        TEST_ASSERT(monitor.ShouldOptimizeRendering(), "Should suggest rendering optimization at low FPS");
        TEST_ASSERT(monitor.ShouldReduceAnimations(), "Should suggest reducing animations at very low FPS");
        
        // Test draw call recording
        monitor.ResetRenderingStats();
        monitor.RecordDrawCall(100, 300); // 100 triangles, 300 vertices
        monitor.RecordDrawCall(50, 150);
        
        const auto& updatedMetrics = monitor.GetCurrentMetrics();
        TEST_ASSERT(updatedMetrics.drawCalls == 2, "Should record 2 draw calls");
        TEST_ASSERT(updatedMetrics.triangles == 150, "Should record total triangles");
        TEST_ASSERT(updatedMetrics.vertices == 450, "Should record total vertices");
        
        // Test performance callback
        bool callbackCalled = false;
        monitor.RegisterPerformanceCallback([&](const PerformanceMetrics& metrics) {
            callbackCalled = true;
        });
        
        monitor.BeginFrame();
        monitor.EndFrame();
        
        TEST_ASSERT(callbackCalled, "Performance callback should be called");
        
        std::cout << "✓ PerformanceMonitor tests passed" << std::endl;
        return true;
    }

    bool TestPerformanceUtils() {
        std::cout << "Testing PerformanceUtils..." << std::endl;
        
        PerformanceMetrics testMetrics;
        testMetrics.frameRate = 25.0f;
        testMetrics.cpuUsage = 85.0f;
        testMetrics.memoryUsage = 100 * 1024 * 1024; // 100MB
        
        // Test optimization suggestions
        TEST_ASSERT(PerformanceUtils::ShouldReduceQuality(testMetrics), 
                   "Should suggest quality reduction at low FPS and high CPU");
        TEST_ASSERT(PerformanceUtils::ShouldDisableAnimations(testMetrics) == false, 
                   "Should not disable animations at 25 FPS");
        TEST_ASSERT(PerformanceUtils::ShouldReduceUpdateFrequency(testMetrics.cpuUsage), 
                   "Should reduce update frequency at high CPU usage");
        
        // Test animation speed adjustment
        float animSpeed = PerformanceUtils::GetOptimalAnimationSpeed(testMetrics);
        TEST_ASSERT(animSpeed < 1.0f, "Animation speed should be reduced at low FPS");
        
        // Test memory optimization
        size_t maxMemory = 80 * 1024 * 1024; // 80MB max
        TEST_ASSERT(PerformanceUtils::ShouldGarbageCollect(testMetrics.memoryUsage, maxMemory),
                   "Should suggest garbage collection when memory usage is high");
        
        std::cout << "✓ PerformanceUtils tests passed" << std::endl;
        return true;
    }

    bool TestPerformanceProfiler() {
        std::cout << "Testing PerformanceProfiler..." << std::endl;
        
        PerformanceProfiler::ClearProfiles();
        
        // Test basic profiling
        {
            PerformanceProfiler profiler("TestOperation");
            profiler.Begin();
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
            profiler.End();
        }
        
        auto results = PerformanceProfiler::GetProfileResults();
        TEST_ASSERT(results.size() == 1, "Should have 1 profile result");
        TEST_ASSERT(results[0].first == "TestOperation", "Profile name should match");
        TEST_ASSERT(results[0].second >= 10.0f, "Profile time should be at least 10ms");
        
        // Test RAII profiling
        {
            PROFILE_SCOPE("RAIITest");
            std::this_thread::sleep_for(std::chrono::milliseconds(5));
        }
        
        results = PerformanceProfiler::GetProfileResults();
        TEST_ASSERT(results.size() == 2, "Should have 2 profile results");
        
        std::cout << "✓ PerformanceProfiler tests passed" << std::endl;
        return true;
    }

    bool TestKeyboardNavigation() {
        std::cout << "Testing KeyboardNavigation..." << std::endl;
        
        KeyboardNavigation& nav = KeyboardNavigation::GetInstance();
        nav.ClearAllElements();
        
        // Test element registration
        KeyboardNavigation::NavigableElement element1("button1", ImVec2(10, 10), ImVec2(100, 30));
        KeyboardNavigation::NavigableElement element2("button2", ImVec2(10, 50), ImVec2(100, 30));
        
        bool element1Focused = false;
        bool element2Focused = false;
        
        element1.onFocus = [&]() { element1Focused = true; };
        element2.onFocus = [&]() { element2Focused = true; };
        
        nav.RegisterElement(element1);
        nav.RegisterElement(element2);
        
        // Test focus setting
        nav.SetFocusedElement("button1");
        TEST_ASSERT(nav.GetFocusedElement() == "button1", "Should focus button1");
        TEST_ASSERT(element1Focused, "Element1 focus callback should be called");
        
        // Test navigation
        nav.Navigate(KeyboardNavigation::NavigationDirection::Down);
        TEST_ASSERT(nav.GetFocusedElement() == "button2", "Should navigate to button2");
        TEST_ASSERT(element2Focused, "Element2 focus callback should be called");
        
        std::cout << "✓ KeyboardNavigation tests passed" << std::endl;
        return true;
    }

    bool TestAccessibleTooltip() {
        std::cout << "Testing AccessibleTooltip..." << std::endl;
        
        AccessibleTooltip& tooltip = AccessibleTooltip::GetInstance();
        tooltip.HideAllTooltips();
        
        // Test tooltip creation
        AccessibleTooltip::TooltipInfo info("Test tooltip");
        info.detailedDescription = "Detailed description";
        info.preferredPosition = ImVec2(100, 100);
        
        tooltip.ShowTooltip("test_tooltip", info);
        
        // Note: We can't easily test rendering without ImGui context,
        // but we can test the basic functionality
        
        tooltip.HideTooltip("test_tooltip");
        
        // Test tooltip enabling/disabling
        tooltip.SetTooltipsEnabled(false);
        tooltip.ShowTooltip("disabled_tooltip", info);
        // Tooltip should not be shown when disabled
        
        tooltip.SetTooltipsEnabled(true);
        
        std::cout << "✓ AccessibleTooltip tests passed" << std::endl;
        return true;
    }

    bool TestScreenReaderSupport() {
        std::cout << "Testing ScreenReaderSupport..." << std::endl;
        
        ScreenReaderSupport& screenReader = ScreenReaderSupport::GetInstance();
        
        // Test enabling/disabling
        screenReader.SetEnabled(true);
        TEST_ASSERT(screenReader.IsEnabled(), "Screen reader should be enabled");
        
        // Test announcements (output goes to console in test implementation)
        screenReader.Announce("Test announcement");
        screenReader.AnnounceElementFocus("button", "Test Button");
        screenReader.AnnounceStateChange("checkbox", "checked");
        
        screenReader.SetEnabled(false);
        TEST_ASSERT(!screenReader.IsEnabled(), "Screen reader should be disabled");
        
        std::cout << "✓ ScreenReaderSupport tests passed" << std::endl;
        return true;
    }

    bool TestAccessibilitySystem() {
        std::cout << "Testing AccessibilitySystem..." << std::endl;
        
        AccessibilitySystem& accessibility = AccessibilitySystem::GetInstance();
        
        // Test configuration
        AccessibilityConfig config;
        config.highContrastMode = true;
        config.largeTextMode = true;
        config.textScale = 1.5f;
        config.keyboardNavigationEnabled = true;
        
        accessibility.SetConfig(config);
        
        const auto& retrievedConfig = accessibility.GetConfig();
        TEST_ASSERT(retrievedConfig.highContrastMode, "High contrast mode should be enabled");
        TEST_ASSERT(retrievedConfig.largeTextMode, "Large text mode should be enabled");
        TEST_ASSERT(retrievedConfig.textScale == 1.5f, "Text scale should be 1.5");
        
        // Test individual settings
        accessibility.SetHighContrastMode(false);
        TEST_ASSERT(!accessibility.GetConfig().highContrastMode, "High contrast should be disabled");
        
        accessibility.SetTextScale(2.0f);
        TEST_ASSERT(accessibility.GetConfig().textScale == 2.0f, "Text scale should be updated");
        
        // Test color accessibility
        ImVec4 originalColor(0.5f, 0.5f, 0.5f, 1.0f);
        ImVec4 accessibleColor = accessibility.GetAccessibleColor(originalColor);
        // Color should be modified for accessibility
        
        // Test text size accessibility
        float originalSize = 12.0f;
        float accessibleSize = accessibility.GetAccessibleTextSize(originalSize);
        TEST_ASSERT(accessibleSize == originalSize * 2.0f, "Text size should be scaled");
        
        std::cout << "✓ AccessibilitySystem tests passed" << std::endl;
        return true;
    }

    // Run all performance and accessibility tests
    bool RunAllPerformanceAccessibilityTests() {
        std::cout << "=== Running Performance and Accessibility Tests ===" << std::endl;
        
        bool allPassed = true;
        
        allPassed &= TestPerformanceMonitor();
        allPassed &= TestPerformanceUtils();
        allPassed &= TestPerformanceProfiler();
        allPassed &= TestKeyboardNavigation();
        allPassed &= TestAccessibleTooltip();
        allPassed &= TestScreenReaderSupport();
        allPassed &= TestAccessibilitySystem();
        
        if (allPassed) {
            std::cout << "\n✓ All performance and accessibility tests passed!" << std::endl;
        } else {
            std::cout << "\n✗ Some performance and accessibility tests failed!" << std::endl;
        }
        
        return allPassed;
    }

} // namespace test
} // namespace ui
