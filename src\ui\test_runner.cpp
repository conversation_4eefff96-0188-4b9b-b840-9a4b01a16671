#include "test_security_event_handling.cpp"
#include "test_performance_accessibility.cpp"
#include "test_security_ui_integration.cpp"
#include "test_security_dashboard.cpp"
#include <iostream>
#include <chrono>

namespace ui {
namespace test {

    // Test suite runner
    class TestSuiteRunner {
    public:
        static void RunAllTests() {
            std::cout << "========================================" << std::endl;
            std::cout << "    NEBULA LOADER UI TEST SUITE" << std::endl;
            std::cout << "========================================" << std::endl;
            
            auto startTime = std::chrono::steady_clock::now();
            
            bool allTestsPassed = true;
            int totalSuites = 0;
            int passedSuites = 0;
            
            // Run Security Event Handling Tests
            std::cout << "\n[1/5] Security Event Handling Tests" << std::endl;
            std::cout << "------------------------------------" << std::endl;
            totalSuites++;
            try {
                if (RunAllSecurityEventHandlingTests()) {
                    passedSuites++;
                    std::cout << "✓ Security Event Handling: PASSED" << std::endl;
                } else {
                    allTestsPassed = false;
                    std::cout << "✗ Security Event Handling: FAILED" << std::endl;
                }
            } catch (const std::exception& e) {
                allTestsPassed = false;
                std::cout << "✗ Security Event Handling: EXCEPTION - " << e.what() << std::endl;
            }
            
            // Run Performance and Accessibility Tests
            std::cout << "\n[2/5] Performance and Accessibility Tests" << std::endl;
            std::cout << "----------------------------------------" << std::endl;
            totalSuites++;
            try {
                if (RunAllPerformanceAccessibilityTests()) {
                    passedSuites++;
                    std::cout << "✓ Performance and Accessibility: PASSED" << std::endl;
                } else {
                    allTestsPassed = false;
                    std::cout << "✗ Performance and Accessibility: FAILED" << std::endl;
                }
            } catch (const std::exception& e) {
                allTestsPassed = false;
                std::cout << "✗ Performance and Accessibility: EXCEPTION - " << e.what() << std::endl;
            }
            
            // Run Security-UI Integration Tests
            std::cout << "\n[3/5] Security-UI Integration Tests" << std::endl;
            std::cout << "-----------------------------------" << std::endl;
            totalSuites++;
            try {
                SecurityUIIntegrationTestSuite::RunAllIntegrationTests();
                passedSuites++;
                std::cout << "✓ Security-UI Integration: PASSED" << std::endl;
            } catch (const std::exception& e) {
                allTestsPassed = false;
                std::cout << "✗ Security-UI Integration: EXCEPTION - " << e.what() << std::endl;
            }
            
            // Run Security Dashboard Tests
            std::cout << "\n[4/5] Security Dashboard Tests" << std::endl;
            std::cout << "------------------------------" << std::endl;
            totalSuites++;
            try {
                if (RunAllSecurityDashboardTests()) {
                    passedSuites++;
                    std::cout << "✓ Security Dashboard: PASSED" << std::endl;
                } else {
                    allTestsPassed = false;
                    std::cout << "✗ Security Dashboard: FAILED" << std::endl;
                }
            } catch (const std::exception& e) {
                allTestsPassed = false;
                std::cout << "✗ Security Dashboard: EXCEPTION - " << e.what() << std::endl;
            }
            
            // Run Animation and UI Polish Tests
            std::cout << "\n[5/5] Animation and UI Polish Tests" << std::endl;
            std::cout << "-----------------------------------" << std::endl;
            totalSuites++;
            try {
                if (RunAnimationAndPolishTests()) {
                    passedSuites++;
                    std::cout << "✓ Animation and UI Polish: PASSED" << std::endl;
                } else {
                    allTestsPassed = false;
                    std::cout << "✗ Animation and UI Polish: FAILED" << std::endl;
                }
            } catch (const std::exception& e) {
                allTestsPassed = false;
                std::cout << "✗ Animation and UI Polish: EXCEPTION - " << e.what() << std::endl;
            }
            
            auto endTime = std::chrono::steady_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
            
            // Print final results
            std::cout << "\n========================================" << std::endl;
            std::cout << "           TEST RESULTS SUMMARY" << std::endl;
            std::cout << "========================================" << std::endl;
            std::cout << "Total Test Suites: " << totalSuites << std::endl;
            std::cout << "Passed: " << passedSuites << std::endl;
            std::cout << "Failed: " << (totalSuites - passedSuites) << std::endl;
            std::cout << "Success Rate: " << (static_cast<float>(passedSuites) / totalSuites * 100.0f) << "%" << std::endl;
            std::cout << "Execution Time: " << duration.count() << "ms" << std::endl;
            
            if (allTestsPassed) {
                std::cout << "\n🎉 ALL TESTS PASSED! 🎉" << std::endl;
                std::cout << "The Nebula Loader UI system is ready for deployment." << std::endl;
            } else {
                std::cout << "\n❌ SOME TESTS FAILED ❌" << std::endl;
                std::cout << "Please review the failed tests and fix the issues." << std::endl;
            }
            
            std::cout << "========================================" << std::endl;
        }
        
    private:
        // Additional test for animation and UI polish features
        static bool RunAnimationAndPolishTests() {
            std::cout << "Testing animation and UI polish features..." << std::endl;
            
            bool allPassed = true;
            
            // Test loading animations
            try {
                // Test spinner animation
                float spinnerValue = Animation::Transitions::SpinnerAnimation(1.0f, 2.0f);
                if (spinnerValue < 0.0f || spinnerValue > 2.0f * 3.14159f) {
                    std::cout << "✗ Spinner animation test failed" << std::endl;
                    allPassed = false;
                } else {
                    std::cout << "✓ Spinner animation test passed" << std::endl;
                }
                
                // Test progress bar animation
                float currentProgress = 0.0f;
                float animatedProgress = Animation::Transitions::ProgressBarAnimation(0.5f, currentProgress, 3.0f, 0.016f);
                if (animatedProgress < 0.0f || animatedProgress > 1.0f) {
                    std::cout << "✗ Progress bar animation test failed" << std::endl;
                    allPassed = false;
                } else {
                    std::cout << "✓ Progress bar animation test passed" << std::endl;
                }
                
                // Test security scan animation
                float scanValue = Animation::Transitions::SecurityScanAnimation(1.0f, 0.5f);
                if (scanValue < 0.0f || scanValue > 1.0f) {
                    std::cout << "✗ Security scan animation test failed" << std::endl;
                    allPassed = false;
                } else {
                    std::cout << "✓ Security scan animation test passed" << std::endl;
                }
                
                // Test threat pulse animation
                float pulseValue = Animation::Transitions::ThreatPulseAnimation(1.0f, 1.0f);
                if (pulseValue < 0.0f || pulseValue > 1.0f) {
                    std::cout << "✗ Threat pulse animation test failed" << std::endl;
                    allPassed = false;
                } else {
                    std::cout << "✓ Threat pulse animation test passed" << std::endl;
                }
                
            } catch (const std::exception& e) {
                std::cout << "✗ Animation tests failed with exception: " << e.what() << std::endl;
                allPassed = false;
            }
            
            // Test UI polish features
            try {
                // Test theme integration
                const auto& theme = Theme::GetCurrentTheme();
                if (theme.primary.w <= 0.0f) {
                    std::cout << "✗ Theme integration test failed" << std::endl;
                    allPassed = false;
                } else {
                    std::cout << "✓ Theme integration test passed" << std::endl;
                }
                
                // Test color blending
                ImVec4 color1(1.0f, 0.0f, 0.0f, 1.0f);
                ImVec4 color2(0.0f, 1.0f, 0.0f, 1.0f);
                ImVec4 blended = Theme::BlendColors(color1, color2, 0.5f);
                if (blended.x < 0.4f || blended.x > 0.6f || blended.y < 0.4f || blended.y > 0.6f) {
                    std::cout << "✗ Color blending test failed" << std::endl;
                    allPassed = false;
                } else {
                    std::cout << "✓ Color blending test passed" << std::endl;
                }
                
            } catch (const std::exception& e) {
                std::cout << "✗ UI polish tests failed with exception: " << e.what() << std::endl;
                allPassed = false;
            }
            
            return allPassed;
        }
    };

} // namespace test
} // namespace ui

// Main test entry point
int main() {
    try {
        ui::test::TestSuiteRunner::RunAllTests();
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "Fatal error running tests: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "Unknown fatal error running tests" << std::endl;
        return 1;
    }
}
