#include "security_integration.hpp"
#include <iostream>
#include <cassert>
#include <thread>
#include <chrono>

// Simple test framework
#define TEST_ASSERT(condition, message) \
    if (!(condition)) { \
        std::cerr << "TEST FAILED: " << message << std::endl; \
        return false; \
    }

namespace ui {
namespace test {

    bool TestSecurityActionTrigger() {
        std::cout << "Testing SecurityActionTrigger..." << std::endl;
        
        SecurityActionTrigger& trigger = SecurityActionTrigger::GetInstance();
        
        // Test action callback registration
        bool callbackCalled = false;
        SecurityActionType receivedAction;
        std::string receivedTarget;
        
        trigger.RegisterActionCallback([&](const SecurityActionTrigger::ActionRequest& request) {
            callbackCalled = true;
            receivedAction = request.action;
            receivedTarget = request.target;
        });
        
        // Test triggering an action
        trigger.TriggerAction(SecurityActionType::RunFullScan, "test_target");
        
        // Give some time for async processing
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
        
        TEST_ASSERT(callbackCalled, "Action callback should be called");
        TEST_ASSERT(receivedAction == SecurityActionType::RunFullScan, "Correct action should be received");
        TEST_ASSERT(receivedTarget == "test_target", "Correct target should be received");
        
        std::cout << "✓ SecurityActionTrigger tests passed" << std::endl;
        return true;
    }

    bool TestSecurityEventLogger() {
        std::cout << "Testing SecurityEventLogger..." << std::endl;
        
        SecurityEventLogger& logger = SecurityEventLogger::GetInstance();
        
        // Clear any existing log entries
        logger.ClearLog();
        
        // Test logging events
        SecurityEvent event1(SecurityEventType::CheckStarted, "Test Check 1", 
                           StatusIndicator::Status::Checking, "Test message 1");
        SecurityEvent event2(SecurityEventType::ThreatDetected, "Test Check 2", 
                           StatusIndicator::Status::Critical, "Test message 2");
        
        logger.LogEvent(event1, "Additional info 1");
        logger.LogEvent(event2, "Additional info 2");
        
        // Test retrieving recent events
        auto recentEvents = logger.GetRecentEvents(10);
        TEST_ASSERT(recentEvents.size() == 2, "Should have 2 recent events");
        TEST_ASSERT(recentEvents[0].event.checkName == "Test Check 1", "First event should match");
        TEST_ASSERT(recentEvents[1].event.checkName == "Test Check 2", "Second event should match");
        
        // Test filtering by event type
        auto threatEvents = logger.GetEventsByType(SecurityEventType::ThreatDetected, 10);
        TEST_ASSERT(threatEvents.size() == 1, "Should have 1 threat event");
        TEST_ASSERT(threatEvents[0].event.checkName == "Test Check 2", "Threat event should match");
        
        // Test action logging
        SecurityActionTrigger::ActionRequest actionRequest(SecurityActionType::RunSpecificCheck, "Test Action");
        logger.LogAction(actionRequest, "Action completed successfully");
        
        auto allEvents = logger.GetRecentEvents(10);
        TEST_ASSERT(allEvents.size() == 3, "Should have 3 events after action logging");
        
        std::cout << "✓ SecurityEventLogger tests passed" << std::endl;
        return true;
    }

    bool TestEnhancedNotificationSystem() {
        std::cout << "Testing Enhanced SecurityNotificationSystem..." << std::endl;
        
        SecurityNotificationSystem& notificationSystem = SecurityNotificationSystem::GetInstance();
        
        // Clear existing notifications
        notificationSystem.ClearAllNotifications();
        
        // Test basic notification
        notificationSystem.AddNotification(SecurityNotificationSystem::NotificationType::Info,
                                         "Test Notification", "Test message");
        
        auto notifications = notificationSystem.GetAllNotifications();
        TEST_ASSERT(notifications.size() == 1, "Should have 1 notification");
        TEST_ASSERT(notifications[0].title == "Test Notification", "Notification title should match");
        
        // Test action notification
        std::vector<SecurityNotificationSystem::NotificationAction> actions;
        actions.emplace_back("Run Scan", SecurityActionType::RunFullScan);
        actions.emplace_back("Ignore", SecurityActionType::IgnoreThreat);
        
        notificationSystem.AddActionNotification("Action Required", "Please choose an action", actions);
        
        notifications = notificationSystem.GetAllNotifications();
        TEST_ASSERT(notifications.size() == 2, "Should have 2 notifications");
        TEST_ASSERT(notifications[1].type == SecurityNotificationSystem::NotificationType::Action, 
                   "Second notification should be action type");
        TEST_ASSERT(notifications[1].actions.size() == 2, "Should have 2 actions");
        
        // Test progress notification
        notificationSystem.AddProgressNotification("Scanning", "Security scan in progress", 0.5f);
        
        notifications = notificationSystem.GetAllNotifications();
        TEST_ASSERT(notifications.size() == 3, "Should have 3 notifications");
        TEST_ASSERT(notifications[2].type == SecurityNotificationSystem::NotificationType::Progress,
                   "Third notification should be progress type");
        TEST_ASSERT(notifications[2].progress == 0.5f, "Progress should be 0.5");
        
        // Test updating progress notification
        notificationSystem.UpdateProgressNotification(2, 0.8f, "Almost complete");
        notifications = notificationSystem.GetAllNotifications();
        TEST_ASSERT(notifications[2].progress == 0.8f, "Progress should be updated to 0.8");
        TEST_ASSERT(notifications[2].message == "Almost complete", "Message should be updated");
        
        // Test critical count
        notificationSystem.AddNotification(SecurityNotificationSystem::NotificationType::Critical,
                                         "Critical Alert", "Critical security issue");
        
        size_t criticalCount = notificationSystem.GetCriticalCount();
        TEST_ASSERT(criticalCount == 1, "Should have 1 critical notification");
        
        // Test executing notification action
        bool actionExecuted = false;
        SecurityActionTrigger::GetInstance().RegisterActionCallback([&](const SecurityActionTrigger::ActionRequest& request) {
            if (request.action == SecurityActionType::RunFullScan) {
                actionExecuted = true;
            }
        });
        
        notificationSystem.ExecuteNotificationAction(1, 0); // Execute first action of second notification
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
        
        TEST_ASSERT(actionExecuted, "Action should be executed when notification action is triggered");
        
        std::cout << "✓ Enhanced SecurityNotificationSystem tests passed" << std::endl;
        return true;
    }

    bool TestSecurityIntegrationWithActions() {
        std::cout << "Testing SecurityIntegration with action handling..." << std::endl;
        
        SecurityIntegration& security = SecurityIntegration::GetInstance();
        
        // Test that action handling is properly integrated
        bool actionHandled = false;
        std::string handledAction;
        
        // Register a callback to monitor security events
        security.RegisterEventCallback([&](const SecurityEvent& event) {
            if (event.type == SecurityEventType::ConfigurationChanged) {
                actionHandled = true;
            }
        });
        
        // Trigger an action through the action trigger system
        SecurityActionTrigger::GetInstance().TriggerAction(SecurityActionType::RunFullScan);
        
        // Give some time for processing
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        // Check that notifications were created
        auto notifications = SecurityNotificationSystem::GetInstance().GetAllNotifications();
        bool foundProgressNotification = false;
        bool foundCompletionNotification = false;
        
        for (const auto& notification : notifications) {
            if (notification.type == SecurityNotificationSystem::NotificationType::Progress &&
                notification.title == "Full Security Scan") {
                foundProgressNotification = true;
            }
            if (notification.type == SecurityNotificationSystem::NotificationType::Success &&
                notification.title == "Full Scan Complete") {
                foundCompletionNotification = true;
            }
        }
        
        TEST_ASSERT(foundProgressNotification, "Should create progress notification for full scan");
        TEST_ASSERT(foundCompletionNotification, "Should create completion notification for full scan");
        
        std::cout << "✓ SecurityIntegration with action handling tests passed" << std::endl;
        return true;
    }

    bool TestEventLoggingIntegration() {
        std::cout << "Testing event logging integration..." << std::endl;
        
        SecurityEventLogger& logger = SecurityEventLogger::GetInstance();
        SecurityIntegration& security = SecurityIntegration::GetInstance();
        
        // Clear log
        logger.ClearLog();
        
        // Run a security check to generate events
        security.RunSecurityCheck("Debugger Detection");
        
        // Give some time for processing
        std::this_thread::sleep_for(std::chrono::milliseconds(50));
        
        // Check that events were logged
        auto recentEvents = logger.GetRecentEvents(10);
        TEST_ASSERT(recentEvents.size() > 0, "Should have logged events from security check");
        
        bool foundCheckStarted = false;
        bool foundCheckCompleted = false;
        
        for (const auto& entry : recentEvents) {
            if (entry.event.type == SecurityEventType::CheckStarted &&
                entry.event.checkName == "Debugger Detection") {
                foundCheckStarted = true;
            }
            if (entry.event.type == SecurityEventType::CheckCompleted &&
                entry.event.checkName == "Debugger Detection") {
                foundCheckCompleted = true;
            }
        }
        
        TEST_ASSERT(foundCheckStarted, "Should log check started event");
        TEST_ASSERT(foundCheckCompleted, "Should log check completed event");
        
        std::cout << "✓ Event logging integration tests passed" << std::endl;
        return true;
    }

    // Run all security event handling tests
    bool RunAllSecurityEventHandlingTests() {
        std::cout << "=== Running Security Event Handling Tests ===" << std::endl;
        
        bool allPassed = true;
        
        allPassed &= TestSecurityActionTrigger();
        allPassed &= TestSecurityEventLogger();
        allPassed &= TestEnhancedNotificationSystem();
        allPassed &= TestSecurityIntegrationWithActions();
        allPassed &= TestEventLoggingIntegration();
        
        if (allPassed) {
            std::cout << "\n✓ All security event handling tests passed!" << std::endl;
        } else {
            std::cout << "\n✗ Some security event handling tests failed!" << std::endl;
        }
        
        return allPassed;
    }

} // namespace test
} // namespace ui
