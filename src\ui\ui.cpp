// Prevent Windows socket header conflicts
#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif
#ifndef NOMINMAX
#define NOMINMAX
#endif

#include "ui.hpp"
#include "components.hpp"
#include "theme.hpp"
#include "animation.hpp"
#include "security_integration.hpp"
#include "performance_monitor.hpp"
#include "accessibility.hpp"
#include "../globals.hpp"
#include <algorithm>

// Prevent Windows macro conflicts
#ifdef min
#undef min
#endif
#ifdef max
#undef max
#endif

namespace ui {

    // Global UI state
    static UIState g_uiState;
    static LPDIRECT3DDEVICE9 g_device = nullptr;
    static bool g_initialized = false;
    static std::unique_ptr<MainWindowLayout> g_mainLayout;

    void init(LPDIRECT3DDEVICE9 device) {
        if (g_initialized) return;
        
        g_device = device;
        
        // Initialize UI state
        g_uiState = UIState();
        
        // Initialize window manager and load saved state
        WindowManager& windowManager = WindowManager::GetInstance();
        windowManager.LoadWindowState();
        
        // Update UI state with window manager values
        g_uiState.windowSize = windowManager.GetWindowSize();
        g_uiState.windowPosition = windowManager.GetWindowPosition();
        g_uiState.minWindowSize = windowManager.GetMinWindowSize();
        g_uiState.maxWindowSize = windowManager.GetMaxWindowSize();
        
        // Apply default theme
        SetDarkTheme();
        ApplyTheme();
        
        // Initialize main window layout first
        g_mainLayout = std::make_unique<MainWindowLayout>();
        g_mainLayout->SetWindowSize(g_uiState.windowSize);
        g_mainLayout->SetWindowPosition(g_uiState.windowPosition);

        // Initialize performance monitoring
        PerformanceMonitor& perfMonitor = PerformanceMonitor::GetInstance();
        perfMonitor.SetTargetFrameRate(60.0f);
        perfMonitor.RegisterPerformanceCallback([](const PerformanceMetrics& metrics) {
            // Handle performance events if needed
            if (metrics.frameRate < 30.0f) {
                // Could trigger performance optimizations here
            }
        });

        // Initialize accessibility system
        AccessibilitySystem& accessibility = AccessibilitySystem::GetInstance();
        accessibility.Initialize();

        // Initialize security integration system safely (can be disabled for debugging)
#ifndef DISABLE_SECURITY_INTEGRATION
        try {
            SecurityIntegration& security = SecurityIntegration::GetInstance();
            security.Initialize();

            // Connect security system to dashboard
            if (auto dashboard = g_mainLayout->GetSecurityDashboard()) {
                security.ConnectToSecurityDashboard(dashboard);

                // Set up security event callback
                security.RegisterEventCallback([](const SecurityEvent& event) {
                    try {
                        // Handle security events - could log, show notifications, etc.
                        if (event.type == SecurityEventType::ThreatDetected) {
                            SecurityNotificationSystem::GetInstance().AddNotification(
                                SecurityNotificationSystem::NotificationType::Critical,
                                "Security Threat",
                                event.checkName + ": " + event.message,
                                0.0f // Persistent
                            );
                        }
                    } catch (...) {
                        // Ignore callback errors to prevent crashes
                    }
                });

                // Start real-time monitoring (but don't crash if it fails)
                try {
                    security.StartRealTimeMonitoring();
                } catch (...) {
                    // Monitoring failed, continue without it
                }
            }
        } catch (...) {
            // Security integration failed, continue without it
            // The UI will still work, just without security features
        }
#endif

        // Register layout update callback with window manager
        windowManager.SetLayoutUpdateCallback([&]() {
            if (g_mainLayout) {
                g_mainLayout->SetWindowSize(windowManager.GetWindowSize());
                g_mainLayout->SetWindowPosition(windowManager.GetWindowPosition());
            }
        });
        
        // Configure main layout components
        if (auto header = g_mainLayout->GetHeader()) {
            header->SetTitle("Nebula Loader");
            header->SetSubtitle("Security Management System");
            header->SetSecurityStatus("Active");
        }
        
        if (auto footer = g_mainLayout->GetFooter()) {
            footer->SetVersion("v1.0.0");
            footer->SetStatus("System Ready");
            footer->SetBuildInfo("Build 2024.01.18");
            footer->SetConnectionStatus("Connected");
            footer->SetShowDetailedInfo(true);
            footer->SetMemoryUsage(45.2f); // Example: 45.2 MB
            footer->SetCpuUsage(12.5f);    // Example: 12.5% CPU
        }
        
        // Run initial security checks to populate the dashboard
#ifndef DISABLE_SECURITY_INTEGRATION
        try {
            if (auto dashboard = g_mainLayout->GetSecurityDashboard()) {
                // Run all security checks to get real data
                SecurityIntegration::GetInstance().RunAllSecurityChecks();
            }
        } catch (...) {
            // Ignore if security checks fail
        }
#endif
        
        g_initialized = true;
    }

    void render() {
        if (!g_initialized) return;

        // Begin performance monitoring
        PerformanceMonitor& perfMonitor = PerformanceMonitor::GetInstance();
        perfMonitor.BeginFrame();

        // Update animation time
        static auto lastTime = std::chrono::steady_clock::now();
        auto currentTime = std::chrono::steady_clock::now();
        float deltaTime = std::chrono::duration<float>(currentTime - lastTime).count();
        lastTime = currentTime;

        g_uiState.globalAnimationTime += deltaTime;

        // Update accessibility system
        AccessibilitySystem::GetInstance().Update();
        
        // Update window management
        WindowManager& windowManager = WindowManager::GetInstance();
        windowManager.Update(deltaTime);
        
        // Handle window events and resize
        HandleWindowEvents();
        
        // Update animation system
        Animation::GetAnimationSystem().Update(deltaTime);
        
        // Update and render main window layout
        if (g_mainLayout) {
            g_mainLayout->Update(deltaTime);
            g_mainLayout->Render();
            
            // Check for close request from header controls
            if (auto header = g_mainLayout->GetHeader()) {
                if (header->ShouldClose()) {
                    g_uiState.isMainWindowVisible = false;
                    header->ResetCloseFlag();
                }
            }
            
            // Update UI state from main layout and window manager
            g_uiState.windowSize = windowManager.GetWindowSize();
            g_uiState.windowPosition = windowManager.GetWindowPosition();
        } else {
            // Fallback: render simple window if main layout is not available
            ImGui::SetNextWindowPos(g_uiState.windowPosition, ImGuiCond_FirstUseEver);
            ImGui::SetNextWindowSize(g_uiState.windowSize, ImGuiCond_FirstUseEver);
            
            if (ImGui::Begin("Nebula Loader", &g_uiState.isMainWindowVisible)) {
                ImGui::Text("Main layout not initialized");
                
                static ModernButton exitBtn("fallback_exit", "Exit");
                exitBtn.SetStyle(ModernButton::Style::Danger);
                if (exitBtn.Render(ImVec2(100, 30))) {
                    globals.active = false;
                }
            }
            ImGui::End();
        }
        
        // Render security notifications (overlay) - safely
#ifndef DISABLE_SECURITY_INTEGRATION
        try {
            SecurityNotificationSystem::GetInstance().RenderNotificationPopups();
        } catch (...) {
            // Ignore notification rendering errors to prevent crashes
        }
#endif

        // Render performance overlay (optional)
        static bool showPerformanceOverlay = false;
        if (ImGui::IsKeyPressed(ImGuiKey_F3)) {
            showPerformanceOverlay = !showPerformanceOverlay;
        }
        if (showPerformanceOverlay) {
            perfMonitor.RenderPerformanceOverlay();
        }

        // Close application if main window is closed
        if (!g_uiState.isMainWindowVisible) {
            globals.active = false;
        }

        // End performance monitoring
        perfMonitor.EndFrame();
        perfMonitor.UpdateMemoryUsage();
        perfMonitor.UpdateCPUUsage();
        perfMonitor.ResetRenderingStats();
    }

    void shutdown() {
        // Shutdown accessibility system
        AccessibilitySystem::GetInstance().Shutdown();

        // Shutdown security integration safely
#ifndef DISABLE_SECURITY_INTEGRATION
        try {
            SecurityIntegration::GetInstance().Shutdown();
        } catch (...) {
            // Ignore shutdown errors
        }
#endif

        // Save window state before shutdown
        SaveWindowState();

        g_mainLayout.reset();
        g_initialized = false;
        g_device = nullptr;
    }

    UIState& GetUIState() {
        return g_uiState;
    }

    void ApplyTheme() {
        // Theme application will be implemented in theme.cpp
        // This is a placeholder that calls the theme system
        Theme::ApplyCurrentTheme();
    }

    void SetDarkTheme() {
        // Dark theme setup will be implemented in theme.cpp
        Theme::SetDarkTheme();
    }

    void SaveWindowState() {
        WindowManager::GetInstance().SaveWindowState();
    }

    void LoadWindowState() {
        WindowManager::GetInstance().LoadWindowState();
    }

    void HandleWindowEvents() {
        WindowManager::GetInstance().HandleWindowResize();
        WindowManager::GetInstance().Update(0.016f); // Assume 60fps
    }

    // LayoutManager Implementation
    void LayoutManager::BeginMainLayout() {
        ImGuiViewport* viewport = ImGui::GetMainViewport();
        ImGui::SetNextWindowPos(viewport->WorkPos);
        ImGui::SetNextWindowSize(viewport->WorkSize);
        ImGui::SetNextWindowViewport(viewport->ID);

        ImGuiWindowFlags window_flags = ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoCollapse |
                                       ImGuiWindowFlags_NoResize | ImGuiWindowFlags_NoMove |
                                       ImGuiWindowFlags_NoBringToFrontOnFocus | ImGuiWindowFlags_NoNavFocus;

        ImGui::PushStyleVar(ImGuiStyleVar_WindowRounding, 0.0f);
        ImGui::PushStyleVar(ImGuiStyleVar_WindowBorderSize, 0.0f);
        ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(0.0f, 0.0f));

        ImGui::Begin("MainLayoutWindow", nullptr, window_flags);
    }

    void LayoutManager::EndMainLayout() {
        ImGui::End();
        ImGui::PopStyleVar(3);
    }

    ImVec2 LayoutManager::GetContentAreaSize() {
        ImVec2 windowSize = ImGui::GetWindowSize();
        float headerHeight = CalculateResponsiveHeaderHeight();
        float footerHeight = CalculateResponsiveFooterHeight();
        float padding = CalculateResponsivePadding();

        return ImVec2(
            windowSize.x - (padding * 2),
            windowSize.y - headerHeight - footerHeight - (padding * 2)
        );
    }

    ImVec2 LayoutManager::GetContentAreaPosition() {
        ImVec2 windowPos = ImGui::GetWindowPos();
        float headerHeight = CalculateResponsiveHeaderHeight();
        float padding = CalculateResponsivePadding();

        return ImVec2(
            windowPos.x + padding,
            windowPos.y + headerHeight + padding
        );
    }

    void LayoutManager::HandleWindowResize() {
        WindowManager& wm = WindowManager::GetInstance();
        ImVec2 currentSize = ImGui::GetWindowSize();

        if (currentSize.x != wm.GetWindowSize().x || currentSize.y != wm.GetWindowSize().y) {
            wm.SetWindowSize(currentSize);
        }
    }

    bool LayoutManager::IsResponsiveBreakpoint() {
        ImVec2 windowSize = ImGui::GetWindowSize();
        return windowSize.x < 768.0f; // Mobile/tablet breakpoint
    }

    float LayoutManager::CalculateResponsiveHeaderHeight() {
        return IsResponsiveBreakpoint() ? 45.0f : 60.0f;
    }

    float LayoutManager::CalculateResponsiveFooterHeight() {
        return IsResponsiveBreakpoint() ? 25.0f : 30.0f;
    }

    float LayoutManager::CalculateResponsivePadding() {
        return IsResponsiveBreakpoint() ? 8.0f : 16.0f;
    }

    // Static member definitions
    bool LayoutManager::s_compactMode = false;
    float LayoutManager::s_scaleFactor = 1.0f;

    void LayoutManager::SetLayoutMode(bool compactMode) {
        s_compactMode = compactMode;
    }

    bool LayoutManager::IsCompactMode() {
        return s_compactMode;
    }

    float LayoutManager::GetScaleFactor() {
        return s_scaleFactor;
    }

    void LayoutManager::SetScaleFactor(float scale) {
        s_scaleFactor = (std::max)(0.5f, (std::min)(scale, 3.0f)); // Clamp between 0.5x and 3.0x
    }

    // WindowUtils Implementation
    ImVec2 WindowUtils::s_primaryScreenSize = ImVec2(1920, 1080);
    bool WindowUtils::s_screenSizeCached = false;

    ImVec2 WindowUtils::GetPrimaryScreenSize() {
        if (!s_screenSizeCached) {
            // In a real implementation, this would query the actual screen size
            // For now, we use a default value
            s_primaryScreenSize = ImVec2(1920, 1080);
            s_screenSizeCached = true;
        }
        return s_primaryScreenSize;
    }

    ImVec2 WindowUtils::GetWorkAreaSize() {
        ImVec2 screenSize = GetPrimaryScreenSize();
        // Subtract taskbar and window decorations (approximate)
        return ImVec2(screenSize.x, screenSize.y - 40);
    }

    std::vector<ImVec2> WindowUtils::GetAllScreenSizes() {
        // In a real implementation, this would enumerate all monitors
        return { GetPrimaryScreenSize() };
    }

    int WindowUtils::GetScreenCount() {
        return static_cast<int>(GetAllScreenSizes().size());
    }

    ImVec2 WindowUtils::CalculateCenteredPosition(const ImVec2& windowSize, const ImVec2& screenSize) {
        return ImVec2(
            (screenSize.x - windowSize.x) / 2.0f,
            (screenSize.y - windowSize.y) / 2.0f
        );
    }

    ImVec2 WindowUtils::CalculateOptimalSize(const ImVec2& contentSize, float aspectRatio) {
        ImVec2 optimalSize = contentSize;

        if (aspectRatio > 0.0f) {
            // Adjust size to maintain aspect ratio
            float currentRatio = optimalSize.x / optimalSize.y;
            if (currentRatio > aspectRatio) {
                optimalSize.x = optimalSize.y * aspectRatio;
            } else {
                optimalSize.y = optimalSize.x / aspectRatio;
            }
        }

        // Add padding for window decorations
        optimalSize.x += 32; // Border padding
        optimalSize.y += 64; // Title bar + border padding

        return optimalSize;
    }

    bool WindowUtils::IsPositionOnScreen(const ImVec2& position, const ImVec2& windowSize) {
        ImVec2 screenSize = GetPrimaryScreenSize();

        // Check if at least 25% of the window is visible
        float minVisibleWidth = windowSize.x * 0.25f;
        float minVisibleHeight = windowSize.y * 0.25f;

        return (position.x + minVisibleWidth > 0 && position.x < screenSize.x - minVisibleWidth &&
                position.y + minVisibleHeight > 0 && position.y < screenSize.y - minVisibleHeight);
    }

    std::string WindowUtils::WindowStateToString(bool isMaximized, bool isMinimized, bool isVisible) {
        if (!isVisible) return "Hidden";
        if (isMaximized) return "Maximized";
        if (isMinimized) return "Minimized";
        return "Normal";
    }

    ImVec2 WindowUtils::ClampToScreen(const ImVec2& position, const ImVec2& windowSize) {
        ImVec2 screenSize = GetPrimaryScreenSize();
        ImVec2 clampedPos = position;

        // Ensure window doesn't go completely off-screen
        float minVisibleWidth = (std::min)(windowSize.x * 0.2f, 100.0f);
        float minVisibleHeight = (std::min)(windowSize.y * 0.2f, 50.0f);

        clampedPos.x = (std::max)(-windowSize.x + minVisibleWidth,
                      (std::min)(clampedPos.x, screenSize.x - minVisibleWidth));
        clampedPos.y = (std::max)(-windowSize.y + minVisibleHeight,
                      (std::min)(clampedPos.y, screenSize.y - minVisibleHeight));

        return clampedPos;
    }

    // WindowManager Implementation
    WindowManager& WindowManager::GetInstance() {
        static WindowManager instance;
        return instance;
    }

    void WindowManager::SetWindowPosition(const ImVec2& position) {
        ImVec2 clampedPosition = ClampWindowPosition(position);
        if (m_windowPosition.x != clampedPosition.x || m_windowPosition.y != clampedPosition.y) {
            m_lastWindowPosition = m_windowPosition;
            m_windowPosition = clampedPosition;
            m_windowMoved = true;
            
            if (m_autoSave) {
                SaveWindowState();
            }
        }
    }

    void WindowManager::SetWindowSize(const ImVec2& size) {
        ImVec2 adjustedSize = size;
        
        // Maintain aspect ratio if enabled
        if (m_maintainAspectRatio && m_aspectRatio > 0.0f) {
            float currentRatio = adjustedSize.x / adjustedSize.y;
            if (std::abs(currentRatio - m_aspectRatio) > 0.01f) {
                // Adjust height to maintain aspect ratio
                adjustedSize.y = adjustedSize.x / m_aspectRatio;
            }
        }
        
        ImVec2 clampedSize = ClampWindowSize(adjustedSize);
        if (m_windowSize.x != clampedSize.x || m_windowSize.y != clampedSize.y) {
            m_lastWindowSize = m_windowSize;
            m_windowSize = clampedSize;
            m_windowResized = true;
            
            // Update maximized state
            m_isMaximized = (m_windowSize.x >= m_maxWindowSize.x - 1.0f && 
                            m_windowSize.y >= m_maxWindowSize.y - 1.0f);
            
            // Update UI state
            GetUIState().windowSize = m_windowSize;
            GetUIState().windowResized = true;
            
            // Trigger resize callback
            if (m_resizeCallback) {
                m_resizeCallback(m_windowSize);
            }
            
            // Trigger layout update
            TriggerLayoutUpdate();
            
            if (m_autoSave) {
                SaveWindowState();
            }
        }
    }

    ImVec2 WindowManager::GetWindowPosition() const {
        return m_windowPosition;
    }

    ImVec2 WindowManager::GetWindowSize() const {
        return m_windowSize;
    }

    void WindowManager::SetMinWindowSize(const ImVec2& minSize) {
        m_minWindowSize = minSize;
        // Ensure current size meets minimum requirements
        if (m_windowSize.x < minSize.x || m_windowSize.y < minSize.y) {
            SetWindowSize(ImVec2(
                (std::max)(m_windowSize.x, minSize.x),
                (std::max)(m_windowSize.y, minSize.y)
            ));
        }
    }

    void WindowManager::SetMaxWindowSize(const ImVec2& maxSize) {
        m_maxWindowSize = maxSize;
        // Ensure current size doesn't exceed maximum
        if (m_windowSize.x > maxSize.x || m_windowSize.y > maxSize.y) {
            SetWindowSize(ImVec2(
                (std::min)(m_windowSize.x, maxSize.x),
                (std::min)(m_windowSize.y, maxSize.y)
            ));
        }
    }

    ImVec2 WindowManager::GetMinWindowSize() const {
        return m_minWindowSize;
    }

    ImVec2 WindowManager::GetMaxWindowSize() const {
        return m_maxWindowSize;
    }
    
    void WindowManager::SetWindowConstraints(const ImVec2& minSize, const ImVec2& maxSize) {
        SetMinWindowSize(minSize);
        SetMaxWindowSize(maxSize);
    }
    
    ImVec2 WindowManager::GetConstrainedSize(const ImVec2& size) const {
        return ClampWindowSize(size);
    }
    
    ImVec2 WindowManager::GetConstrainedPosition(const ImVec2& position) const {
        return ClampWindowPosition(position);
    }

    ImVec2 WindowManager::GetConstrainedPosition(const ImVec2& position, const ImVec2& windowSize) const {
        // Get display bounds (assuming primary monitor for now)
        // In a real implementation, you'd get actual monitor bounds
        ImVec2 displaySize(1920, 1080); // Default display size

        // Ensure window stays within display bounds
        ImVec2 constrainedPos = position;

        // Don't let window go too far off-screen (allow some overlap)
        float maxOffscreenX = windowSize.x * 0.8f; // Allow 80% of window to go off-screen
        float maxOffscreenY = windowSize.y * 0.8f;

        float maxX = displaySize.x - (windowSize.x - maxOffscreenX);
        float maxY = displaySize.y - (windowSize.y - maxOffscreenY);
        constrainedPos.x = (std::max)(-maxOffscreenX, (std::min)(constrainedPos.x, maxX));
        constrainedPos.y = (std::max)(-maxOffscreenY, (std::min)(constrainedPos.y, maxY));

        return constrainedPos;
    }

    bool WindowManager::SaveWindowState(const std::string& filename) {
        try {
            WindowStateData data;
            data.posX = m_windowPosition.x;
            data.posY = m_windowPosition.y;
            data.sizeX = m_windowSize.x;
            data.sizeY = m_windowSize.y;
            data.minSizeX = m_minWindowSize.x;
            data.minSizeY = m_minWindowSize.y;
            data.maxSizeX = m_maxWindowSize.x;
            data.maxSizeY = m_maxWindowSize.y;
            data.restoredPosX = m_restoredPosition.x;
            data.restoredPosY = m_restoredPosition.y;
            data.restoredSizeX = m_restoredSize.x;
            data.restoredSizeY = m_restoredSize.y;
            data.visible = m_windowVisible;
            data.isMaximized = m_isMaximized;
            data.isMinimized = m_isMinimized;
            data.maintainAspectRatio = m_maintainAspectRatio;
            data.aspectRatio = m_aspectRatio;
            data.presetCount = static_cast<uint32_t>(m_positionPresets.size());
            data.checksum = CalculateChecksum(data);
            
            std::ofstream file(filename, std::ios::binary);
            if (!file.is_open()) {
                return false;
            }
            
            // Write main data
            file.write(reinterpret_cast<const char*>(&data), sizeof(WindowStateData));
            
            // Write position presets
            for (const auto& preset : m_positionPresets) {
                // Write preset name length and name
                uint32_t nameLength = static_cast<uint32_t>(preset.first.length());
                file.write(reinterpret_cast<const char*>(&nameLength), sizeof(uint32_t));
                file.write(preset.first.c_str(), nameLength);
                
                // Write position and size
                file.write(reinterpret_cast<const char*>(&preset.second.first), sizeof(ImVec2));
                file.write(reinterpret_cast<const char*>(&preset.second.second), sizeof(ImVec2));
            }
            
            file.close();
            return true;
        }
        catch (...) {
            return false;
        }
    }

    bool WindowManager::LoadWindowState(const std::string& filename) {
        try {
            std::ifstream file(filename, std::ios::binary);
            if (!file.is_open()) {
                return false;
            }
            
            WindowStateData data;
            file.read(reinterpret_cast<char*>(&data), sizeof(WindowStateData));
            
            // Validate data integrity
            if (!ValidateWindowStateData(data)) {
                file.close();
                return false;
            }
            
            // Apply loaded state
            m_windowPosition = ImVec2(data.posX, data.posY);
            m_windowSize = ImVec2(data.sizeX, data.sizeY);
            m_minWindowSize = ImVec2(data.minSizeX, data.minSizeY);
            m_maxWindowSize = ImVec2(data.maxSizeX, data.maxSizeY);
            m_restoredPosition = ImVec2(data.restoredPosX, data.restoredPosY);
            m_restoredSize = ImVec2(data.restoredSizeX, data.restoredSizeY);
            m_windowVisible = data.visible;
            m_isMaximized = data.isMaximized;
            m_isMinimized = data.isMinimized;
            m_maintainAspectRatio = data.maintainAspectRatio;
            m_aspectRatio = data.aspectRatio;
            
            // Load position presets
            m_positionPresets.clear();
            for (uint32_t i = 0; i < data.presetCount; ++i) {
                // Read preset name
                uint32_t nameLength;
                file.read(reinterpret_cast<char*>(&nameLength), sizeof(uint32_t));
                
                if (nameLength > 256) { // Sanity check
                    file.close();
                    return false;
                }
                
                std::string presetName(nameLength, '\0');
                file.read(&presetName[0], nameLength);
                
                // Read position and size
                ImVec2 position, size;
                file.read(reinterpret_cast<char*>(&position), sizeof(ImVec2));
                file.read(reinterpret_cast<char*>(&size), sizeof(ImVec2));
                
                m_positionPresets[presetName] = std::make_pair(position, size);
            }
            
            file.close();
            
            // Enforce constraints
            EnforceWindowConstraints();
            
            // Update UI state
            GetUIState().windowPosition = m_windowPosition;
            GetUIState().windowSize = m_windowSize;
            GetUIState().minWindowSize = m_minWindowSize;
            GetUIState().maxWindowSize = m_maxWindowSize;
            GetUIState().isMainWindowVisible = m_windowVisible;
            
            return true;
        }
        catch (...) {
            return false;
        }
    }

    void WindowManager::HandleWindowResize() {
        // Get current ImGui window size if available
        if (ImGui::GetCurrentContext()) {
            ImGuiIO& io = ImGui::GetIO();
            ImVec2 displaySize = io.DisplaySize;
            
            // Check if display size changed
            if (displaySize.x != m_windowSize.x || displaySize.y != m_windowSize.y) {
                SetWindowSize(displaySize);
            }
        }
        
        // Update UI state
        UIState& state = GetUIState();
        if (state.windowResized) {
            SetWindowSize(state.windowSize);
            state.windowResized = false;
        }
        
        if (state.windowMoved) {
            SetWindowPosition(state.windowPosition);
            state.windowMoved = false;
        }
    }

    void WindowManager::RegisterResizeCallback(std::function<void(ImVec2)> callback) {
        m_resizeCallback = callback;
    }

    void WindowManager::UnregisterResizeCallback() {
        m_resizeCallback = nullptr;
    }



    void WindowManager::UpdateLayout() {
        if (m_layoutUpdateCallback) {
            m_layoutUpdateCallback();
        }
        
        // Update main layout if available
        if (g_mainLayout) {
            g_mainLayout->SetWindowSize(m_windowSize);
            g_mainLayout->SetWindowPosition(m_windowPosition);
        }
    }

    void WindowManager::SetLayoutUpdateCallback(std::function<void()> callback) {
        m_layoutUpdateCallback = callback;
    }

    void WindowManager::TriggerLayoutUpdate() {
        UpdateLayout();
    }

    bool WindowManager::IsWindowMinimized() const {
        return m_isMinimized;
    }

    bool WindowManager::IsWindowMaximized() const {
        return m_isMaximized;
    }

    bool WindowManager::IsWindowVisible() const {
        return m_windowVisible;
    }

    void WindowManager::SetWindowVisible(bool visible) {
        if (m_windowVisible != visible) {
            m_windowVisible = visible;
            GetUIState().isMainWindowVisible = visible;
            
            if (m_autoSave) {
                SaveWindowState();
            }
        }
    }

    bool WindowManager::IsWindowCentered() const {
        // Check if window is approximately centered on screen
        // Assuming screen size of 1920x1080 for calculation
        ImVec2 screenCenter(960, 540);
        ImVec2 windowCenter(m_windowPosition.x + m_windowSize.x / 2, m_windowPosition.y + m_windowSize.y / 2);
        float distance = std::sqrt(std::pow(screenCenter.x - windowCenter.x, 2) + std::pow(screenCenter.y - windowCenter.y, 2));
        return distance < 50.0f; // Within 50 pixels of center
    }

    bool WindowManager::IsAspectRatioMaintained() const {
        return m_maintainAspectRatio;
    }

    // Advanced window controls implementation
    void WindowManager::CenterWindow() {
        // Center window on primary screen (assuming 1920x1080)
        ImVec2 screenSize(1920, 1080);
        ImVec2 centeredPos(
            (screenSize.x - m_windowSize.x) / 2,
            (screenSize.y - m_windowSize.y) / 2
        );
        SetWindowPosition(centeredPos);
    }

    void WindowManager::MaximizeWindow() {
        if (!m_isMaximized) {
            // Store current position and size for restoration
            m_restoredPosition = m_windowPosition;
            m_restoredSize = m_windowSize;
            
            // Set to maximum size
            SetWindowSize(m_maxWindowSize);
            SetWindowPosition(ImVec2(0, 0));
            
            m_isMaximized = true;
            m_isMinimized = false;
            
            if (m_autoSave) {
                SaveWindowState();
            }
        }
    }

    void WindowManager::RestoreWindow() {
        if (m_isMaximized || m_isMinimized) {
            // Restore to previous position and size
            SetWindowPosition(m_restoredPosition);
            SetWindowSize(m_restoredSize);
            
            m_isMaximized = false;
            m_isMinimized = false;
            
            if (m_autoSave) {
                SaveWindowState();
            }
        }
    }

    void WindowManager::MinimizeWindow() {
        if (!m_isMinimized) {
            // Store current position and size for restoration
            m_restoredPosition = m_windowPosition;
            m_restoredSize = m_windowSize;
            
            m_isMinimized = true;
            m_isMaximized = false;
            SetWindowVisible(false);
            
            if (m_autoSave) {
                SaveWindowState();
            }
        }
    }

    void WindowManager::MoveWindowToScreen(int screenIndex) {
        // Simple implementation for primary screen (screenIndex 0)
        // In a full implementation, this would query actual screen information
        if (screenIndex == 0) {
            // Move to primary screen
            ImVec2 screenOffset(0, 0);
            ImVec2 newPos = m_windowPosition;
            
            // Ensure window is within screen bounds
            if (newPos.x < screenOffset.x) newPos.x = screenOffset.x;
            if (newPos.y < screenOffset.y) newPos.y = screenOffset.y;
            if (newPos.x + m_windowSize.x > screenOffset.x + 1920) {
                newPos.x = screenOffset.x + 1920 - m_windowSize.x;
            }
            if (newPos.y + m_windowSize.y > screenOffset.y + 1080) {
                newPos.y = screenOffset.y + 1080 - m_windowSize.y;
            }
            
            SetWindowPosition(newPos);
        }
    }

    void WindowManager::FitWindowToContent(const ImVec2& contentSize) {
        // Add padding for window decorations and UI elements
        ImVec2 padding(32, 64); // Account for borders, title bar, etc.
        ImVec2 newSize = ImVec2(contentSize.x + padding.x, contentSize.y + padding.y);
        
        // Ensure size meets constraints
        newSize = ClampWindowSize(newSize);
        SetWindowSize(newSize);
    }

    void WindowManager::SetWindowAspectRatio(float ratio, bool maintain) {
        m_aspectRatio = ratio;
        m_maintainAspectRatio = maintain;
        
        if (maintain) {
            // Adjust current window size to match aspect ratio
            float currentRatio = m_windowSize.x / m_windowSize.y;
            if (std::abs(currentRatio - ratio) > 0.01f) {
                // Adjust height to match width and desired ratio
                ImVec2 newSize(m_windowSize.x, m_windowSize.x / ratio);
                SetWindowSize(newSize);
            }
        }
    }

    float WindowManager::GetWindowAspectRatio() const {
        return m_windowSize.x / m_windowSize.y;
    }

    // Window position presets
    void WindowManager::SetWindowPositionPreset(const std::string& presetName, const ImVec2& position, const ImVec2& size) {
        m_positionPresets[presetName] = std::make_pair(position, size);
        
        if (m_autoSave) {
            SaveWindowState();
        }
    }

    bool WindowManager::ApplyWindowPositionPreset(const std::string& presetName) {
        auto it = m_positionPresets.find(presetName);
        if (it != m_positionPresets.end()) {
            SetWindowPosition(it->second.first);
            SetWindowSize(it->second.second);
            return true;
        }
        return false;
    }

    void WindowManager::RemoveWindowPositionPreset(const std::string& presetName) {
        m_positionPresets.erase(presetName);
        
        if (m_autoSave) {
            SaveWindowState();
        }
    }

    std::vector<std::string> WindowManager::GetWindowPositionPresets() const {
        std::vector<std::string> presets;
        for (const auto& preset : m_positionPresets) {
            presets.push_back(preset.first);
        }
        return presets;
    }

    void WindowManager::EnforceWindowConstraints() {
        m_windowSize = ClampWindowSize(m_windowSize);
        m_windowPosition = ClampWindowPosition(m_windowPosition);
    }

    bool WindowManager::IsValidWindowSize(const ImVec2& size) const {
        return (size.x >= m_minWindowSize.x && size.x <= m_maxWindowSize.x &&
                size.y >= m_minWindowSize.y && size.y <= m_maxWindowSize.y);
    }

    bool WindowManager::IsValidWindowPosition(const ImVec2& position) const {
        // Basic validation - position should be reasonable
        return (position.x >= -100.0f && position.x <= 3000.0f &&
                position.y >= -100.0f && position.y <= 2000.0f);
    }

    ImVec2 WindowManager::ClampWindowSize(const ImVec2& size) const {
        float clampedX = (std::min)(size.x, m_maxWindowSize.x);
        clampedX = (std::max)(m_minWindowSize.x, clampedX);
        float clampedY = (std::min)(size.y, m_maxWindowSize.y);
        clampedY = (std::max)(m_minWindowSize.y, clampedY);
        return ImVec2(clampedX, clampedY);
    }

    ImVec2 WindowManager::ClampWindowPosition(const ImVec2& position) const {
        // Clamp to reasonable screen bounds
        float clampedX = (std::min)(position.x, 3000.0f);
        clampedX = (std::max)(-100.0f, clampedX);
        float clampedY = (std::min)(position.y, 2000.0f);
        clampedY = (std::max)(-100.0f, clampedY);
        return ImVec2(clampedX, clampedY);
    }

    void WindowManager::Update(float deltaTime) {
        // Handle any pending window state changes
        HandleWindowResize();
        
        // Clear flags after processing
        if (m_windowResized) {
            m_windowResized = false;
        }
        if (m_windowMoved) {
            m_windowMoved = false;
        }
    }

    uint32_t WindowManager::CalculateChecksum(const WindowStateData& data) const {
        // Simple checksum calculation
        uint32_t checksum = 0;
        const uint8_t* bytes = reinterpret_cast<const uint8_t*>(&data);
        size_t size = sizeof(WindowStateData) - sizeof(uint32_t); // Exclude checksum field
        
        for (size_t i = 0; i < size; ++i) {
            checksum += bytes[i];
            checksum = (checksum << 1) | (checksum >> 31); // Rotate left
        }
        
        return checksum;
    }

    bool WindowManager::ValidateWindowStateData(const WindowStateData& data) const {
        // Verify checksum
        WindowStateData tempData = data;
        tempData.checksum = 0;
        uint32_t calculatedChecksum = CalculateChecksum(tempData);
        
        if (calculatedChecksum != data.checksum) {
            return false;
        }
        
        // Validate ranges
        if (data.sizeX < 100.0f || data.sizeX > 5000.0f ||
            data.sizeY < 100.0f || data.sizeY > 5000.0f) {
            return false;
        }
        
        if (data.posX < -1000.0f || data.posX > 5000.0f ||
            data.posY < -1000.0f || data.posY > 5000.0f) {
            return false;
        }
        
        return true;
    }

} // namespace ui