#include "ui.hpp"
#include <Windows.h>
#include <string>
#include <thread>
#include <chrono>
#include <cmath>
#include <iostream>
#include <filesystem>
#include <mutex>
#include <atomic>

#include "../auth/auth.hpp"
#include "../auth/utils.hpp"
#include "../auth/skStr.h"
#include "../globals.hpp"
#include "../security/config.hpp"
#include "../imgui/imgui.h"
#include "../imgui/imgui_internal.h"
#include "../security/debug.hpp"
#include "../security/strings.hpp"
#include "../security/macros.hpp"

using namespace KeyAuth;

// Advanced easing functions for smooth animations
float easeInOutCubic(float t) {
    return t < 0.5f ? 4.0f * t * t * t : 1.0f - pow(-2.0f * t + 2.0f, 3.0f) / 2.0f;
}

float easeInOutSine(float t) {
    return -(cos(3.14159f * t) - 1.0f) / 2.0f;
}

float easeInOutQuart(float t) {
    return t < 0.5f ? 8.0f * t * t * t * t : 1.0f - pow(-2.0f * t + 2.0f, 4.0f) / 2.0f;
}

float easeOutElastic(float t) {
    const float c4 = (2.0f * 3.14159f) / 3.0f;
    return t == 0.0f ? 0.0f : t == 1.0f ? 1.0f : pow(2.0f, -10.0f * t) * sin((t * 10.0f - 0.75f) * c4) + 1.0f;
}

// Color utilities for gradients and effects
ImVec4 LerpColor(const ImVec4& a, const ImVec4& b, float t) {
    return ImVec4(
        a.x + (b.x - a.x) * t,
        a.y + (b.y - a.y) * t,
        a.z + (b.z - a.z) * t,
        a.w + (b.w - a.w) * t
    );
}

ImU32 ColorWithAlpha(const ImVec4& color, float alpha) {
    return IM_COL32(
        (int)(color.x * 255),
        (int)(color.y * 255),
        (int)(color.z * 255),
        (int)(alpha * 255)
    );
}

// KeyAuth configuration with enhanced security - ENCRYPTED
std::string name = skCrypt("Nebula").decrypt(); // App name
std::string ownerid = skCrypt("uRI84Vg5PZ").decrypt(); // Account ID
std::string version = skCrypt("1.0").decrypt(); // Application version
std::string url = skCrypt("https://keyauth.win/api/1.3/").decrypt(); // KeyAuth API URL
std::string path = skCrypt("").decrypt(); // Optional path

api KeyAuthApp(name, ownerid, version, url, path);

// Authentication state variables
static bool keyauth_initialized = false;
static bool show_register = false;
static char license_buf[128] = {};
static char tfa_code_buf[16] = {};
static bool requires_2fa = false;
static std::string auth_error_message;
static std::string current_session_token;
static std::chrono::steady_clock::time_point last_activity;

// Threading and state management
static std::mutex auth_mutex;
static std::atomic<bool> auth_completed(false);
static std::string auth_response_message;
static bool auth_success;

// Modern UI Design Constants
namespace UIDesign {
    // Color Palette - Professional Dark Theme with Neon Accents
    const ImVec4 BACKGROUND_PRIMARY = ImVec4(0.06f, 0.06f, 0.08f, 0.98f);
    const ImVec4 BACKGROUND_SECONDARY = ImVec4(0.10f, 0.10f, 0.13f, 1.0f);
    const ImVec4 BACKGROUND_TERTIARY = ImVec4(0.14f, 0.14f, 0.18f, 1.0f);

    const ImVec4 ACCENT_PRIMARY = ImVec4(0.20f, 0.60f, 1.0f, 1.0f);      // Bright Blue
    const ImVec4 ACCENT_SECONDARY = ImVec4(0.40f, 0.20f, 1.0f, 1.0f);    // Purple
    const ImVec4 ACCENT_SUCCESS = ImVec4(0.20f, 0.90f, 0.40f, 1.0f);     // Green
    const ImVec4 ACCENT_WARNING = ImVec4(1.0f, 0.70f, 0.20f, 1.0f);      // Orange
    const ImVec4 ACCENT_DANGER = ImVec4(1.0f, 0.30f, 0.30f, 1.0f);       // Red

    const ImVec4 TEXT_PRIMARY = ImVec4(0.95f, 0.95f, 0.98f, 1.0f);
    const ImVec4 TEXT_SECONDARY = ImVec4(0.70f, 0.70f, 0.75f, 1.0f);
    const ImVec4 TEXT_MUTED = ImVec4(0.50f, 0.50f, 0.55f, 1.0f);

    const ImVec4 BORDER_COLOR = ImVec4(0.25f, 0.25f, 0.30f, 0.8f);
    const ImVec4 BORDER_ACTIVE = ImVec4(0.20f, 0.60f, 1.0f, 0.9f);

    // Sizing and Spacing
    const float WINDOW_ROUNDING = 12.0f;
    const float FRAME_ROUNDING = 8.0f;
    const float BUTTON_ROUNDING = 6.0f;
    const float CHILD_ROUNDING = 10.0f;

    const float PADDING_LARGE = 24.0f;
    const float PADDING_MEDIUM = 16.0f;
    const float PADDING_SMALL = 8.0f;

    const float SPACING_LARGE = 20.0f;
    const float SPACING_MEDIUM = 12.0f;
    const float SPACING_SMALL = 6.0f;

    // Animation timing
    const float ANIMATION_SPEED_FAST = 8.0f;
    const float ANIMATION_SPEED_NORMAL = 4.0f;
    const float ANIMATION_SPEED_SLOW = 2.0f;
}

// UI positioning and state
static ImVec2 window_pos = ImVec2(0, 0);
static ImVec2 window_size = ImVec2(420, 320);  // Larger for better layout
static ImVec2 screen_res;
static LPDIRECT3DDEVICE9 dev = nullptr;

// Animation state
static float ui_animation_time = 0.0f;
static float button_hover_states[10] = {0.0f}; // For multiple button hover animations
static float input_focus_states[10] = {0.0f};  // For input field focus animations

// Modern UI Helper Functions
namespace UIHelpers {

    // Draw gradient background
    void DrawGradientRect(const ImVec2& pos, const ImVec2& size, const ImVec4& color1, const ImVec4& color2, bool horizontal = false) {
        ImDrawList* draw_list = ImGui::GetWindowDrawList();
        ImU32 col1 = ImGui::ColorConvertFloat4ToU32(color1);
        ImU32 col2 = ImGui::ColorConvertFloat4ToU32(color2);

        if (horizontal) {
            draw_list->AddRectFilledMultiColor(pos, ImVec2(pos.x + size.x, pos.y + size.y), col1, col2, col2, col1);
        } else {
            draw_list->AddRectFilledMultiColor(pos, ImVec2(pos.x + size.x, pos.y + size.y), col1, col1, col2, col2);
        }
    }

    // Draw glowing border effect
    void DrawGlowBorder(const ImVec2& pos, const ImVec2& size, const ImVec4& color, float intensity = 1.0f, float rounding = 0.0f) {
        ImDrawList* draw_list = ImGui::GetWindowDrawList();

        for (int i = 0; i < 3; i++) {
            float alpha = (intensity * 0.3f) / (i + 1);
            float thickness = 1.0f + (i * 0.5f);
            ImU32 glow_color = ColorWithAlpha(color, alpha);

            ImVec2 glow_pos = ImVec2(pos.x - i, pos.y - i);
            ImVec2 glow_size = ImVec2(size.x + (i * 2), size.y + (i * 2));

            draw_list->AddRect(glow_pos, ImVec2(glow_pos.x + glow_size.x, glow_pos.y + glow_size.y),
                             glow_color, rounding, 0, thickness);
        }
    }

    // Modern button with hover effects
    bool ModernButton(const char* label, const ImVec2& size, int button_id = 0, bool enabled = true) {
        ImGui::PushID(button_id);

        // Animation state
        float& hover_state = button_hover_states[button_id % 10];
        bool hovered = ImGui::IsItemHovered() && enabled;
        float target = hovered ? 1.0f : 0.0f;
        hover_state += (target - hover_state) * ImGui::GetIO().DeltaTime * UIDesign::ANIMATION_SPEED_FAST;

        // Colors with animation
        ImVec4 base_color = enabled ? UIDesign::ACCENT_PRIMARY : UIDesign::TEXT_MUTED;
        ImVec4 hover_color = UIDesign::ACCENT_SECONDARY;
        ImVec4 current_color = LerpColor(base_color, hover_color, hover_state);

        // Style the button
        ImGui::PushStyleColor(ImGuiCol_Button, current_color);
        ImGui::PushStyleColor(ImGuiCol_ButtonHovered, LerpColor(current_color, hover_color, 0.2f));
        ImGui::PushStyleColor(ImGuiCol_ButtonActive, LerpColor(current_color, UIDesign::ACCENT_SECONDARY, 0.4f));
        ImGui::PushStyleColor(ImGuiCol_Text, UIDesign::TEXT_PRIMARY);

        ImGui::PushStyleVar(ImGuiStyleVar_FrameRounding, UIDesign::BUTTON_ROUNDING);
        ImGui::PushStyleVar(ImGuiStyleVar_FramePadding, ImVec2(UIDesign::PADDING_MEDIUM, UIDesign::PADDING_SMALL));

        bool clicked = ImGui::Button(label, size) && enabled;

        // Draw glow effect when hovered
        if (hover_state > 0.1f) {
            ImVec2 button_pos = ImGui::GetItemRectMin();
            ImVec2 button_size = ImGui::GetItemRectSize();
            DrawGlowBorder(button_pos, button_size, current_color, hover_state, UIDesign::BUTTON_ROUNDING);
        }

        ImGui::PopStyleVar(2);
        ImGui::PopStyleColor(4);
        ImGui::PopID();

        return clicked;
    }

    // Modern input field with focus effects
    bool ModernInputText(const char* label, const char* hint, char* buf, size_t buf_size, int input_id = 0, bool is_password = false) {
        ImGui::PushID(input_id);

        // Animation state
        float& focus_state = input_focus_states[input_id % 10];
        bool focused = ImGui::IsItemFocused();
        float target = focused ? 1.0f : 0.0f;
        focus_state += (target - focus_state) * ImGui::GetIO().DeltaTime * UIDesign::ANIMATION_SPEED_NORMAL;

        // Colors with animation
        ImVec4 base_color = UIDesign::BACKGROUND_TERTIARY;
        ImVec4 focus_color = UIDesign::BACKGROUND_SECONDARY;
        ImVec4 current_color = LerpColor(base_color, focus_color, focus_state);

        ImVec4 border_color = LerpColor(UIDesign::BORDER_COLOR, UIDesign::BORDER_ACTIVE, focus_state);

        // Style the input
        ImGui::PushStyleColor(ImGuiCol_FrameBg, current_color);
        ImGui::PushStyleColor(ImGuiCol_FrameBgHovered, LerpColor(current_color, focus_color, 0.3f));
        ImGui::PushStyleColor(ImGuiCol_FrameBgActive, focus_color);
        ImGui::PushStyleColor(ImGuiCol_Border, border_color);
        ImGui::PushStyleColor(ImGuiCol_Text, UIDesign::TEXT_PRIMARY);

        ImGui::PushStyleVar(ImGuiStyleVar_FrameRounding, UIDesign::FRAME_ROUNDING);
        ImGui::PushStyleVar(ImGuiStyleVar_FramePadding, ImVec2(UIDesign::PADDING_MEDIUM, UIDesign::PADDING_SMALL + 2.0f));
        ImGui::PushStyleVar(ImGuiStyleVar_FrameBorderSize, 1.0f + focus_state);

        bool changed = ImGui::InputTextWithHint(label, hint, buf, buf_size,
                                               is_password ? ImGuiInputTextFlags_Password : 0);

        // Draw glow effect when focused
        if (focus_state > 0.1f) {
            ImVec2 input_pos = ImGui::GetItemRectMin();
            ImVec2 input_size = ImGui::GetItemRectSize();
            DrawGlowBorder(input_pos, input_size, UIDesign::BORDER_ACTIVE, focus_state * 0.6f, UIDesign::FRAME_ROUNDING);
        }

        ImGui::PopStyleVar(3);
        ImGui::PopStyleColor(5);
        ImGui::PopID();

        return changed;
    }

    // Modern closed ring spinner like professional apps
    void DrawModernSpinner(const ImVec2& center, float radius, float thickness = 4.0f) {
        ImDrawList* draw_list = ImGui::GetWindowDrawList();

        // Background ring (full circle)
        draw_list->AddCircle(center, radius, ColorWithAlpha(UIDesign::BACKGROUND_TERTIARY, 0.3f), 64, thickness * 0.5f);

        // Animated progress arc
        float progress = fmodf(ui_animation_time * 2.0f, 2.0f * 3.14159f);
        float arc_length = 1.5f; // Length of the moving arc

        // Draw the main spinning arc
        for (int i = 0; i < 48; i++) {
            float angle = progress + (i * arc_length / 48.0f);
            float alpha = 1.0f - (float)i / 48.0f; // Fade from full to transparent

            ImVec2 pos1 = ImVec2(center.x + cosf(angle) * radius, center.y + sinf(angle) * radius);
            ImVec2 pos2 = ImVec2(center.x + cosf(angle + 0.05f) * radius, center.y + sinf(angle + 0.05f) * radius);

            ImU32 color = ColorWithAlpha(UIDesign::ACCENT_PRIMARY, alpha * 0.9f);
            draw_list->AddLine(pos1, pos2, color, thickness);
        }

        // Outer glow effect
        for (int glow = 1; glow <= 3; glow++) {
            float glow_radius = radius + (glow * 2.0f);
            float glow_alpha = 0.15f / glow;

            for (int i = 0; i < 32; i++) {
                float angle = progress + (i * arc_length / 32.0f);
                float alpha = (1.0f - (float)i / 32.0f) * glow_alpha;

                ImVec2 pos = ImVec2(center.x + cosf(angle) * glow_radius, center.y + sinf(angle) * glow_radius);
                ImU32 color = ColorWithAlpha(UIDesign::ACCENT_PRIMARY, alpha);
                draw_list->AddCircleFilled(pos, thickness * 0.3f, color);
            }
        }

        // Inner bright core that pulses
        float pulse = 0.7f + 0.3f * sinf(ui_animation_time * 4.0f);
        ImU32 core_color = ColorWithAlpha(UIDesign::ACCENT_SECONDARY, pulse * 0.4f);
        draw_list->AddCircleFilled(center, radius * 0.3f, core_color);

        // Leading edge highlight
        ImVec2 leading_pos = ImVec2(center.x + cosf(progress) * radius, center.y + sinf(progress) * radius);
        draw_list->AddCircleFilled(leading_pos, thickness * 1.2f, ImGui::ColorConvertFloat4ToU32(UIDesign::ACCENT_SECONDARY));

        // Trailing glow
        for (int i = 1; i <= 8; i++) {
            float trail_angle = progress - (i * 0.1f);
            float trail_alpha = 0.6f / i;
            ImVec2 trail_pos = ImVec2(center.x + cosf(trail_angle) * radius, center.y + sinf(trail_angle) * radius);
            ImU32 trail_color = ColorWithAlpha(UIDesign::ACCENT_PRIMARY, trail_alpha);
            draw_list->AddCircleFilled(trail_pos, thickness * (1.0f - i * 0.1f), trail_color);
        }
    }

    // Alternative: Modern Material Design spinner
    void DrawMaterialSpinner(const ImVec2& center, float radius, float thickness = 3.0f) {
        ImDrawList* draw_list = ImGui::GetWindowDrawList();

        // Background circle
        draw_list->AddCircle(center, radius, ColorWithAlpha(UIDesign::BACKGROUND_TERTIARY, 0.2f), 64, thickness * 0.4f);

        // Animated arc that grows and shrinks
        float time = ui_animation_time * 1.5f;
        float arc_start = fmodf(time * 2.0f, 2.0f * 3.14159f);
        float arc_size = 0.5f + 0.5f * sinf(time * 3.0f); // Arc size oscillates

        // Draw the arc
        int segments = (int)(arc_size * 64);
        for (int i = 0; i < segments; i++) {
            float angle = arc_start + (i * arc_size * 2.0f * 3.14159f / 64.0f);
            float next_angle = arc_start + ((i + 1) * arc_size * 2.0f * 3.14159f / 64.0f);

            ImVec2 pos1 = ImVec2(center.x + cosf(angle) * radius, center.y + sinf(angle) * radius);
            ImVec2 pos2 = ImVec2(center.x + cosf(next_angle) * radius, center.y + sinf(next_angle) * radius);

            // Gradient along the arc
            float progress = (float)i / segments;
            ImU32 color = ColorWithAlpha(LerpColor(UIDesign::ACCENT_PRIMARY, UIDesign::ACCENT_SECONDARY, progress), 0.9f);
            draw_list->AddLine(pos1, pos2, color, thickness);
        }

        // End caps
        if (segments > 0) {
            float start_angle = arc_start;
            float end_angle = arc_start + (arc_size * 2.0f * 3.14159f);

            ImVec2 start_pos = ImVec2(center.x + cosf(start_angle) * radius, center.y + sinf(start_angle) * radius);
            ImVec2 end_pos = ImVec2(center.x + cosf(end_angle) * radius, center.y + sinf(end_angle) * radius);

            draw_list->AddCircleFilled(start_pos, thickness * 0.6f, ImGui::ColorConvertFloat4ToU32(UIDesign::ACCENT_PRIMARY));
            draw_list->AddCircleFilled(end_pos, thickness * 0.6f, ImGui::ColorConvertFloat4ToU32(UIDesign::ACCENT_SECONDARY));
        }
    }
}

// Function to add KEYAUTH- prefix if not already present
std::string formatLicenseKey(const std::string& key) {
    if (key.empty()) return key;
    if (key.substr(0, 8) == skCrypt("KEYAUTH-").decrypt()) {
        return key; // Already has prefix
    }
    return skCrypt("KEYAUTH-").decrypt() + key;
}

// Username encoding/decoding functions
std::string generateUserHash(const std::string& username) {
    uint32_t hash = 0;
    for (char c : username) {
        hash = hash * 31 + static_cast<uint32_t>(c);
    }
    std::stringstream ss;
    ss << skCrypt("CS_").decrypt() << std::hex << hash;
    return ss.str();
}

std::string encodeUsername(const std::string& real_username) {
    std::string hash = generateUserHash(real_username);
    return hash + skCrypt("_").decrypt() + real_username;
}

std::string decodeUsername(const std::string& encoded_username) {
    #if defined(DEV)
    std::cout << "DECODE DEBUG: Input '" << encoded_username << "'\n";
    #endif

    size_t separator_pos = encoded_username.find('_');
    if (separator_pos != std::string::npos && separator_pos > 0) {
        separator_pos = encoded_username.find_last_of('_');
        std::string extracted_username = encoded_username.substr(separator_pos + 1);
        std::string hash_part = encoded_username.substr(0, separator_pos);
        std::string expected_hash = generateUserHash(extracted_username);

        if (hash_part == expected_hash) {
            return extracted_username;
        }
    }
    return encoded_username; // Fallback
}

// Case-sensitive username validation
bool validateUsernameCaseSensitive(const std::string& input_username) {
    #if defined(DEV)
    std::cout << "PRE-CHECK: User input '" << input_username << "' wird zu '" << encodeUsername(input_username) << "' encoded\n";
    #endif
    return true;
}

// Authentication thread function
void auth_thread_func(bool is_register, std::string user, std::string pass, std::string key) {
    INTEGRITY_CHECK();
    TIMING_CHECK();
    FLOW_OBFUSCATE();

    if (is_register) {
        std::string formatted_key = formatLicenseKey(key);
        std::string encoded_user = encodeUsername(user);
        
        #if defined(DEV)
        std::cout << "=== REGISTER ENCODING ===\n";
        std::cout << "Original: '" << user << "'\n";
        std::cout << "Encoded: '" << encoded_user << "'\n";
        std::cout << "=========================\n";
        #endif

        KeyAuthApp.regstr(encoded_user, pass, formatted_key);

        if (KeyAuthApp.response.success) {
            WriteToJson(skCrypt("auth.json").decrypt(), skCrypt("username").decrypt(), user, true, skCrypt("password").decrypt(), pass);
        }
    } else {
        if (!validateUsernameCaseSensitive(user)) {
            auth_success = false;
            auth_response_message = skCrypt("Invalid credentials").decrypt();
            return;
        }

        std::string encoded_user = encodeUsername(user);
        
        #if defined(DEV)
        std::cout << "=== LOGIN ENCODING ===\n";
        std::cout << "Original: '" << user << "'\n";
        std::cout << "Encoded: '" << encoded_user << "'\n";
        std::cout << "======================\n";
        #endif

        KeyAuthApp.login(encoded_user, pass);

        if (KeyAuthApp.response.success) {
            std::string decoded_keyauth_user = decodeUsername(KeyAuthApp.user_data.username);

            if (user != decoded_keyauth_user) {
                #if defined(DEV)
                std::cout << "❌ REJECTED: Input '" << user << "' != Registered '" << decoded_keyauth_user << "'\n";
                #endif
                auth_success = false;
                auth_response_message = skCrypt("Invalid credentials").decrypt();
                KeyAuthApp.response.success = false;
                return;
            }

            WriteToJson(skCrypt("auth.json").decrypt(), skCrypt("username").decrypt(), user, true, skCrypt("password").decrypt(), pass);
            current_session_token = user + std::to_string(std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::steady_clock::now().time_since_epoch()).count());
            last_activity = std::chrono::steady_clock::now();
        }
    }

    std::lock_guard<std::mutex> lock(auth_mutex);
    auth_success = KeyAuthApp.response.success;
    auth_response_message = KeyAuthApp.response.message;
    auth_completed = true;

    // Clear sensitive data
    Obfuscation::MemoryProtection::ClearSensitiveData((void*)user.data(), user.size());
    Obfuscation::MemoryProtection::ClearSensitiveData((void*)pass.data(), pass.size());
    Obfuscation::MemoryProtection::ClearSensitiveData((void*)key.data(), key.size());
}

// Session management functions
std::string tm_to_readable_time(tm ctx);
static std::time_t string_to_timet(std::string timestamp);
static std::tm timet_to_tm(time_t timestamp);
void sessionStatus();

// Initialize KeyAuth with auto-login support
void initializeKeyAuth() {
    if (!keyauth_initialized) {
        INTEGRITY_CHECK();
        TIMING_CHECK();
        FLOW_OBFUSCATE();

        KeyAuthApp.init();
        keyauth_initialized = true;

        if (!KeyAuthApp.response.success) {
            auth_error_message = skCrypt("Failed to connect to Server").decrypt();
            return;
        }

        // Check for saved credentials
        std::string auth_json_path = skCrypt("auth.json").decrypt();
        if (std::filesystem::exists(auth_json_path)) {
            if (CheckIfJsonKeyExists(auth_json_path, skCrypt("username").decrypt())) {
                std::string saved_username = ReadFromJson(auth_json_path, skCrypt("username").decrypt());
                std::string saved_password = ReadFromJson(auth_json_path, skCrypt("password").decrypt());

                strcpy_s(globals.user_name, saved_username.c_str());
                strcpy_s(globals.pass_word, saved_password.c_str());

                std::thread(auth_thread_func, false, saved_username, saved_password, skCrypt("").decrypt()).detach();
                globals.login_in_progress = true;
            }
        }
        JUNK_CODE_1;
    }
}

namespace ui {

void render() {
    if (!globals.active) return;

    // Update animation time
    ui_animation_time += ImGui::GetIO().DeltaTime * UIDesign::ANIMATION_SPEED_NORMAL;

    // Keep session alive during active use
    static auto last_activity_update = std::chrono::steady_clock::now();
    auto now = std::chrono::steady_clock::now();
    auto time_since_update = std::chrono::duration_cast<std::chrono::seconds>(now - last_activity_update);

    if (time_since_update.count() > 10) {
        last_activity = now;
        last_activity_update = now;

        if (globals.logged_in && !current_session_token.empty()) {
            last_activity = now;
        }
    }

    // Security checks
    static int security_counter = 0;
    if (++security_counter % 100 == 0) {
        INTEGRITY_CHECK();
        TIMING_CHECK();
        FLOW_OBFUSCATE();
    }

    initializeKeyAuth();

    static char username_buf[64] = {};
    static char password_buf[64] = {};
    static bool show_password = false;

    // Session timeout check
    if (globals.logged_in && !current_session_token.empty()) {
        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::hours>(now - last_activity);

        // Detect user interactions
        ImGuiIO& io = ImGui::GetIO();
        bool user_active = io.MouseClicked[0] || io.MouseClicked[1] || io.MouseClicked[2] ||
                          io.KeysDownDuration[0] > 0 || io.MouseWheel != 0.0f ||
                          io.MouseDelta.x != 0.0f || io.MouseDelta.y != 0.0f;

        if (user_active) {
            last_activity = now;
        }

        if (elapsed.count() > 24) { // 24 hours timeout
            globals.logged_in = false;
            current_session_token.clear();

            std::string auth_json_path = skCrypt("auth.json").decrypt();
            if (std::filesystem::exists(auth_json_path)) {
                std::filesystem::remove(auth_json_path);
            }

            auth_error_message = skCrypt("Session expired after 24 hours, please login again").decrypt();
        }
    }

    JUNK_CODE_2;

    if (globals.login_in_progress) {
        globals.loading_rotation += ImGui::GetIO().DeltaTime * 6.0f;
    }

    // Check authentication completion
    if (auth_completed) {
        globals.login_in_progress = false;

        std::lock_guard<std::mutex> lock(auth_mutex);
        if (auth_success) {
            globals.logged_in = true;
            strcpy_s(globals.user_name, KeyAuthApp.user_data.username.c_str());
            last_activity = std::chrono::steady_clock::now();

            if (show_register) {
                show_register = false;
            }
            std::thread(sessionStatus).detach();
        } else {
            if (auth_response_message == skCrypt("2FA code required.").decrypt()) {
                requires_2fa = true;
                auth_error_message = skCrypt("2FA code required").decrypt();
            } else if (show_register) {
                auth_error_message = skCrypt("Registration failed - please check your details").decrypt();
                SecureZeroMemory(username_buf, sizeof(username_buf));
                SecureZeroMemory(password_buf, sizeof(password_buf));
                SecureZeroMemory(license_buf, sizeof(license_buf));
            } else {
                auth_error_message = skCrypt("Login failed - invalid credentials").decrypt();
                SecureZeroMemory(username_buf, sizeof(username_buf));
                SecureZeroMemory(password_buf, sizeof(password_buf));
            }
        }
        auth_completed = false;
    }

    // Dynamic window sizing based on state
    ImVec2 window_size_vec = globals.logged_in ? ImVec2(680, 520) : ImVec2(450, 380);
    ImGui::SetNextWindowSize(window_size_vec, ImGuiCond_Always);

    // Apply modern theme with professional styling
    ImGui::StyleColorsDark();

    // Window and background colors
    ImGui::PushStyleColor(ImGuiCol_WindowBg, UIDesign::BACKGROUND_PRIMARY);
    ImGui::PushStyleColor(ImGuiCol_ChildBg, UIDesign::BACKGROUND_SECONDARY);
    ImGui::PushStyleColor(ImGuiCol_PopupBg, UIDesign::BACKGROUND_SECONDARY);

    // Frame and input colors
    ImGui::PushStyleColor(ImGuiCol_FrameBg, UIDesign::BACKGROUND_TERTIARY);
    ImGui::PushStyleColor(ImGuiCol_FrameBgHovered, LerpColor(UIDesign::BACKGROUND_TERTIARY, UIDesign::ACCENT_PRIMARY, 0.1f));
    ImGui::PushStyleColor(ImGuiCol_FrameBgActive, LerpColor(UIDesign::BACKGROUND_TERTIARY, UIDesign::ACCENT_PRIMARY, 0.2f));

    // Button colors (will be overridden by ModernButton)
    ImGui::PushStyleColor(ImGuiCol_Button, UIDesign::ACCENT_PRIMARY);
    ImGui::PushStyleColor(ImGuiCol_ButtonHovered, LerpColor(UIDesign::ACCENT_PRIMARY, UIDesign::ACCENT_SECONDARY, 0.3f));
    ImGui::PushStyleColor(ImGuiCol_ButtonActive, UIDesign::ACCENT_SECONDARY);

    // Border and separator colors
    ImGui::PushStyleColor(ImGuiCol_Border, UIDesign::BORDER_COLOR);
    ImGui::PushStyleColor(ImGuiCol_BorderShadow, ImVec4(0, 0, 0, 0));
    ImGui::PushStyleColor(ImGuiCol_Separator, UIDesign::BORDER_COLOR);

    // Header and tab colors
    ImGui::PushStyleColor(ImGuiCol_Header, UIDesign::BACKGROUND_TERTIARY);
    ImGui::PushStyleColor(ImGuiCol_HeaderHovered, LerpColor(UIDesign::BACKGROUND_TERTIARY, UIDesign::ACCENT_PRIMARY, 0.2f));
    ImGui::PushStyleColor(ImGuiCol_HeaderActive, LerpColor(UIDesign::BACKGROUND_TERTIARY, UIDesign::ACCENT_PRIMARY, 0.3f));

    ImGui::PushStyleColor(ImGuiCol_Tab, UIDesign::BACKGROUND_TERTIARY);
    ImGui::PushStyleColor(ImGuiCol_TabHovered, LerpColor(UIDesign::BACKGROUND_TERTIARY, UIDesign::ACCENT_PRIMARY, 0.3f));
    ImGui::PushStyleColor(ImGuiCol_TabActive, UIDesign::ACCENT_PRIMARY);

    // Text colors
    ImGui::PushStyleColor(ImGuiCol_Text, UIDesign::TEXT_PRIMARY);
    ImGui::PushStyleColor(ImGuiCol_TextDisabled, UIDesign::TEXT_MUTED);

    // Modern styling variables
    ImGui::PushStyleVar(ImGuiStyleVar_WindowRounding, UIDesign::WINDOW_ROUNDING);
    ImGui::PushStyleVar(ImGuiStyleVar_ChildRounding, UIDesign::CHILD_ROUNDING);
    ImGui::PushStyleVar(ImGuiStyleVar_FrameRounding, UIDesign::FRAME_ROUNDING);
    ImGui::PushStyleVar(ImGuiStyleVar_GrabRounding, UIDesign::FRAME_ROUNDING);
    ImGui::PushStyleVar(ImGuiStyleVar_TabRounding, UIDesign::FRAME_ROUNDING);
    ImGui::PushStyleVar(ImGuiStyleVar_ScrollbarRounding, UIDesign::FRAME_ROUNDING);

    ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(UIDesign::PADDING_LARGE, UIDesign::PADDING_LARGE));
    ImGui::PushStyleVar(ImGuiStyleVar_FramePadding, ImVec2(UIDesign::PADDING_MEDIUM, UIDesign::PADDING_SMALL));
    ImGui::PushStyleVar(ImGuiStyleVar_ItemSpacing, ImVec2(UIDesign::SPACING_MEDIUM, UIDesign::SPACING_MEDIUM));
    ImGui::PushStyleVar(ImGuiStyleVar_ItemInnerSpacing, ImVec2(UIDesign::SPACING_SMALL, UIDesign::SPACING_SMALL));

    ImGui::PushStyleVar(ImGuiStyleVar_WindowBorderSize, 0.0f);  // No border, we'll draw custom
    ImGui::PushStyleVar(ImGuiStyleVar_FrameBorderSize, 1.0f);

    ImGuiWindowFlags window_flags = ImGuiWindowFlags_NoResize | ImGuiWindowFlags_NoCollapse |
                                   ImGuiWindowFlags_NoScrollbar | ImGuiWindowFlags_NoSavedSettings |
                                   ImGuiWindowFlags_NoTitleBar;  // Remove title bar for cleaner look
    ImGui::SetNextWindowPos(ImVec2(window_pos.x, window_pos.y), ImGuiCond_Once);
    ImGui::SetNextWindowBgAlpha(1.0f);

    ImGui::Begin(skCrypt("##NebulaLoader").decrypt(), &globals.active, window_flags);

    // Draw custom window border with glow effect
    ImVec2 window_pos_current = ImGui::GetWindowPos();
    ImVec2 window_size_current = ImGui::GetWindowSize();
    UIHelpers::DrawGlowBorder(window_pos_current, window_size_current, UIDesign::ACCENT_PRIMARY, 0.4f, UIDesign::WINDOW_ROUNDING);

    // Draw gradient background overlay
    UIHelpers::DrawGradientRect(window_pos_current, window_size_current,
                               UIDesign::BACKGROUND_PRIMARY,
                               LerpColor(UIDesign::BACKGROUND_PRIMARY, UIDesign::ACCENT_PRIMARY, 0.05f));

    // Enhanced loading animation with professional spinner
    if (globals.login_in_progress) {
        ImVec2 center = ImVec2(window_pos_current.x + window_size_current.x * 0.5f,
                              window_pos_current.y + window_size_current.y * 0.5f);

        // Draw professional closed ring spinner
        UIHelpers::DrawMaterialSpinner(center, 35.0f, 4.0f);

        // Secondary smaller spinner for extra effect
        UIHelpers::DrawModernSpinner(center, 50.0f, 2.5f);

        // Animated loading text with modern styling
        const char* auth_text = skCrypt("Authenticating").decrypt();
        ImVec2 text_size = ImGui::CalcTextSize(auth_text);
        ImVec2 text_pos = ImVec2(center.x - text_size.x * 0.5f, center.y + 85.0f);

        // Text with subtle glow
        ImDrawList* draw_list = ImGui::GetWindowDrawList();
        for (int i = 0; i < 2; i++) {
            ImVec2 glow_offset = ImVec2(i * 0.8f, i * 0.8f);
            float alpha = 0.4f / (i + 1);
            ImU32 glow_color = ColorWithAlpha(UIDesign::ACCENT_PRIMARY, alpha);
            draw_list->AddText(ImVec2(text_pos.x + glow_offset.x, text_pos.y + glow_offset.y), glow_color, auth_text);
        }

        // Main text
        draw_list->AddText(text_pos, ImGui::ColorConvertFloat4ToU32(UIDesign::TEXT_PRIMARY), auth_text);

        // Modern animated dots with pulsing effect
        float dot_time = ui_animation_time * 3.0f;
        for (int i = 0; i < 3; i++) {
            float dot_phase = fmodf(dot_time - i * 0.3f, 2.0f);
            float dot_alpha = dot_phase < 1.0f ? dot_phase : 2.0f - dot_phase;
            dot_alpha = ImMax(0.2f, dot_alpha);

            ImVec2 dot_pos = ImVec2(text_pos.x + text_size.x + 10 + (i * 8), text_pos.y);
            ImU32 dot_color = ColorWithAlpha(UIDesign::ACCENT_PRIMARY, dot_alpha);
            draw_list->AddCircleFilled(dot_pos, 2.5f, dot_color);

            // Dot glow
            if (dot_alpha > 0.5f) {
                draw_list->AddCircleFilled(dot_pos, 4.0f, ColorWithAlpha(UIDesign::ACCENT_PRIMARY, (dot_alpha - 0.5f) * 0.3f));
            }
        }

        // Progress indicator text
        const char* progress_text = skCrypt("Please wait...").decrypt();
        ImVec2 progress_size = ImGui::CalcTextSize(progress_text);
        ImVec2 progress_pos = ImVec2(center.x - progress_size.x * 0.5f, center.y + 110.0f);
        draw_list->AddText(progress_pos, ColorWithAlpha(UIDesign::TEXT_SECONDARY, 0.8f), progress_text);
    }

    if (!globals.login_in_progress) {
        // Header section with logo/title
        ImGui::Spacing();
        ImGui::Spacing();

        // Draw title with gradient effect
        const char* title = skCrypt("NEBULA").decrypt();
        const char* subtitle = skCrypt("Advanced Loader System").decrypt();

        ImVec2 title_size = ImGui::CalcTextSize(title);
        ImVec2 subtitle_size = ImGui::CalcTextSize(subtitle);
        float content_width = ImGui::GetContentRegionAvail().x;

        // Title with glow effect
        ImGui::SetCursorPosX((content_width - title_size.x) * 0.5f);
        ImGui::PushFont(ImGui::GetIO().Fonts->Fonts[0]); // Use default font but larger

        ImDrawList* draw_list = ImGui::GetWindowDrawList();
        ImVec2 title_pos = ImGui::GetCursorScreenPos();

        // Title glow
        for (int i = 0; i < 4; i++) {
            float offset = i * 0.8f;
            float alpha = 0.4f / (i + 1);
            ImU32 glow_color = ColorWithAlpha(UIDesign::ACCENT_PRIMARY, alpha);
            draw_list->AddText(ImVec2(title_pos.x + offset, title_pos.y + offset), glow_color, title);
        }

        // Main title
        ImGui::TextColored(UIDesign::TEXT_PRIMARY, "%s", title);
        ImGui::PopFont();

        // Subtitle
        ImGui::SetCursorPosX((content_width - subtitle_size.x) * 0.5f);
        ImGui::TextColored(UIDesign::TEXT_SECONDARY, "%s", subtitle);

        ImGui::Spacing();
        ImGui::Spacing();

        // Content area with proper spacing
        float item_width = 320.0f;

        if (globals.logged_in) {
            // LOGGED IN UI - Enhanced with modern design
            std::string success_msg = skCrypt("Welcome back, ").decrypt() + (std::string(globals.user_name)) + skCrypt("!").decrypt();
            ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.4f, 0.9f, 0.4f, 1.0f));
            ImVec2 text_size = ImGui::CalcTextSize(success_msg.c_str());
            ImGui::SetCursorPosX((content_width - text_size.x) * 0.5f);
            ImGui::Text("%s", success_msg.c_str());
            ImGui::PopStyleColor();

            ImGui::Spacing();
            ImGui::Separator();
            ImGui::Spacing();

            if (ImGui::BeginTabBar(skCrypt("MainTabs").decrypt(), ImGuiTabBarFlags_None)) {
                if (ImGui::BeginTabItem(skCrypt("Profile").decrypt())) {
                    ImGui::Spacing();

                    // User info with modern styling
                    ImGui::Text(skCrypt("Account Information").decrypt());
                    ImGui::Separator();
                    ImGui::Spacing();

                    ImGui::Text(skCrypt("Username:").decrypt());
                    ImGui::SameLine(120);
                    ImGui::TextColored(ImVec4(0.7f, 0.9f, 1.0f, 1.0f), "%s", KeyAuthApp.user_data.username.c_str());

                    ImGui::Text(skCrypt("Hardware ID:").decrypt());
                    ImGui::SameLine(120);
                    ImGui::TextColored(ImVec4(0.7f, 0.9f, 1.0f, 1.0f), "%s", KeyAuthApp.user_data.hwid.substr(0, 12).c_str());

                    ImGui::Text(skCrypt("Session:").decrypt());
                    ImGui::SameLine(120);
                    ImGui::TextColored(ImVec4(0.7f, 0.9f, 1.0f, 1.0f), "%s", current_session_token.substr(0, 12).c_str());

                    ImGui::EndTabItem();
                }

                if (ImGui::BeginTabItem(skCrypt("Settings").decrypt())) {
                    ImGui::Spacing();
                    ImGui::Text(skCrypt("Application Settings").decrypt());
                    ImGui::Separator();
                    ImGui::Spacing();

                    // Popup Configuration Status with modern styling
                    ImGui::Text(skCrypt("Security Configuration:").decrypt());
                    ImGui::Spacing();

                    #if DISABLE_SECURITY_POPUPS
                        ImGui::TextColored(ImVec4(1.0f, 0.6f, 0.2f, 1.0f), skCrypt("• Security Popups: DISABLED").decrypt());
                    #else
                        ImGui::TextColored(ImVec4(0.4f, 0.9f, 0.4f, 1.0f), skCrypt("• Security Popups: ENABLED").decrypt());
                    #endif

                    #if DISABLE_GUI_POPUPS
                        ImGui::TextColored(ImVec4(1.0f, 0.6f, 0.2f, 1.0f), skCrypt("• GUI Setup Popups: DISABLED").decrypt());
                    #else
                        ImGui::TextColored(ImVec4(0.4f, 0.9f, 0.4f, 1.0f), skCrypt("• GUI Setup Popups: ENABLED").decrypt());
                    #endif

                    ImGui::Spacing();
                    ImGui::Separator();
                    ImGui::Spacing();

                    // Modern buttons with better styling
                    if (ImGui::Button(skCrypt("Clear Stored Credentials").decrypt(), ImVec2(200, 35))) {
                        std::string auth_json_path = skCrypt("auth.json").decrypt();
                        if (std::filesystem::exists(auth_json_path)) {
                            std::filesystem::remove(auth_json_path);
                        }
                    }

                    ImGui::Spacing();

                    ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.8f, 0.3f, 0.3f, 1.0f));
                    ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.9f, 0.4f, 0.4f, 1.0f));
                    ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(0.7f, 0.2f, 0.2f, 1.0f));

                    if (ImGui::Button(skCrypt("Logout").decrypt(), ImVec2(200, 35))) {
                        globals.logged_in = false;
                        current_session_token.clear();

                        std::string auth_json_path = skCrypt("auth.json").decrypt();
                        if (std::filesystem::exists(auth_json_path)) {
                            std::filesystem::remove(auth_json_path);
                        }
                    }

                    ImGui::PopStyleColor(3);
                    ImGui::EndTabItem();
                }
                ImGui::EndTabBar();
            }
        } else if (requires_2fa) {
            // 2FA UI with modern design
            ImGui::Spacing();
            ImVec2 title_size = ImGui::CalcTextSize(skCrypt("Two-Factor Authentication").decrypt());
            ImGui::SetCursorPosX((content_width - title_size.x) * 0.5f);
            ImGui::TextColored(ImVec4(0.9f, 0.9f, 1.0f, 1.0f), skCrypt("Two-Factor Authentication").decrypt());

            ImGui::Spacing();
            ImGui::Spacing();

            float available_width = ImMin(280.0f, ImGui::GetContentRegionAvail().x);
            float start_x = (ImGui::GetContentRegionAvail().x - available_width) * 0.5f;

            ImGui::SetCursorPosX(start_x);
            ImGui::PushItemWidth(available_width);
            ImGui::InputTextWithHint(skCrypt("##2fa_code").decrypt(), skCrypt("Enter 2FA Code").decrypt(), tfa_code_buf, sizeof(tfa_code_buf));
            ImGui::PopItemWidth();
            ImGui::Spacing();
            ImGui::Spacing();

            ImGui::SetCursorPosX(start_x);
            bool tfa_valid = strlen(tfa_code_buf) > 0;

            if (!tfa_valid) {
                ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.3f, 0.3f, 0.3f, 0.6f));
                ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.3f, 0.3f, 0.3f, 0.6f));
                ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(0.3f, 0.3f, 0.3f, 0.6f));
            } else {
                ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.24f, 0.51f, 0.88f, 1.0f));
                ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.30f, 0.57f, 0.94f, 1.0f));
                ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(0.18f, 0.45f, 0.82f, 1.0f));
            }

            if (ImGui::Button(skCrypt("Verify Code").decrypt(), ImVec2(available_width, 40)) && tfa_valid) {
                if (!validateUsernameCaseSensitive(std::string(globals.user_name))) {
                    auth_error_message = skCrypt("Invalid credentials").decrypt();
                    SecureZeroMemory(tfa_code_buf, sizeof(tfa_code_buf));
                } else {
                    std::string encoded_user = encodeUsername(std::string(globals.user_name));
                    KeyAuthApp.login(encoded_user, std::string(globals.pass_word), std::string(tfa_code_buf));

                    if (KeyAuthApp.response.success) {
                        std::string decoded_keyauth_user = decodeUsername(KeyAuthApp.user_data.username);

                        if (std::string(globals.user_name) != decoded_keyauth_user) {
                            auth_error_message = skCrypt("Invalid credentials").decrypt();
                            KeyAuthApp.response.success = false;
                        } else {
                            globals.logged_in = true;
                            requires_2fa = false;
                            WriteToJson(skCrypt("auth.json").decrypt(), skCrypt("username").decrypt(), std::string(globals.user_name), true, skCrypt("password").decrypt(), std::string(globals.pass_word));
                            current_session_token = std::string(globals.user_name) + std::to_string(std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::steady_clock::now().time_since_epoch()).count());
                            last_activity = std::chrono::steady_clock::now();
                        }
                    } else {
                        auth_error_message = skCrypt("Invalid 2FA code").decrypt();
                    }
                    SecureZeroMemory(tfa_code_buf, sizeof(tfa_code_buf));
                }
            }
            ImGui::PopStyleColor(3);
        } else if (show_register) {
            // REGISTER UI with modern design
            ImGui::Spacing();
            ImVec2 title_size = ImGui::CalcTextSize(skCrypt("Create Account").decrypt());
            ImGui::SetCursorPosX((content_width - title_size.x) * 0.5f);
            ImGui::TextColored(ImVec4(0.9f, 0.9f, 1.0f, 1.0f), skCrypt("Create Account").decrypt());

            ImGui::Spacing();
            ImGui::Spacing();

            float available_width = ImMin(item_width, content_width);
            float start_x = (content_width - available_width) * 0.5f;

            ImGui::SetCursorPosX(start_x);
            ImGui::PushItemWidth(available_width);
            ImGui::InputTextWithHint(skCrypt("##reg_username").decrypt(), skCrypt("Username").decrypt(), username_buf, sizeof(username_buf));
            ImGui::PopItemWidth();
            ImGui::Spacing();

            ImGui::SetCursorPosX(start_x);
            ImGui::PushItemWidth(ImMax(0.0f, available_width - 80.0f));
            ImGui::InputTextWithHint(skCrypt("##reg_password").decrypt(), skCrypt("Password").decrypt(), password_buf, sizeof(password_buf), show_password ? 0 : ImGuiInputTextFlags_Password);
            ImGui::PopItemWidth();
            ImGui::SameLine();
            if (ImGui::Button(show_password ? skCrypt("Hide").decrypt() : skCrypt("Show").decrypt(), ImVec2(70, 0))) {
                show_password = !show_password;
            }
            ImGui::Spacing();

            ImGui::SetCursorPosX(start_x);
            ImGui::PushItemWidth(available_width);
            ImGui::InputTextWithHint(skCrypt("##license").decrypt(), skCrypt("License Key").decrypt(), license_buf, sizeof(license_buf));
            ImGui::PopItemWidth();
            ImGui::Spacing();
            ImGui::Spacing();

            ImGui::SetCursorPosX(start_x);
            bool fields_valid = (strlen(username_buf) > 0 && strlen(password_buf) > 0 && strlen(license_buf) > 0);

            if (!fields_valid) {
                ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.3f, 0.3f, 0.3f, 0.6f));
                ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.3f, 0.3f, 0.3f, 0.6f));
                ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(0.3f, 0.3f, 0.3f, 0.6f));
            } else {
                ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.24f, 0.51f, 0.88f, 1.0f));
                ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.30f, 0.57f, 0.94f, 1.0f));
                ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(0.18f, 0.45f, 0.82f, 1.0f));
            }

            if (ImGui::Button(skCrypt("Create Account").decrypt(), ImVec2(available_width, 40)) && fields_valid) {
                auth_error_message = "";
                globals.login_in_progress = true;
                std::thread(auth_thread_func, true, std::string(username_buf), std::string(password_buf), std::string(license_buf)).detach();
            }
            ImGui::PopStyleColor(3);
            ImGui::Spacing();
            ImGui::Spacing();

            // Switch to login link
            std::string switch_text = skCrypt("Already have an account? ").decrypt();
            std::string login_text = skCrypt("Sign In").decrypt();
            ImVec2 switch_size = ImGui::CalcTextSize(switch_text.c_str());
            ImVec2 login_size = ImGui::CalcTextSize(login_text.c_str());
            float total_width = switch_size.x + login_size.x;

            ImGui::SetCursorPosX((content_width - total_width) * 0.5f);
            ImGui::Text("%s", switch_text.c_str());
            ImGui::SameLine(0, 0);
            ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(0.5f, 0.8f, 1.0f, 1.0f));
            if (ImGui::Selectable(login_text.c_str(), false, ImGuiSelectableFlags_DontClosePopups, ImVec2(login_size.x, 0))) {
                show_register = false;
                auth_error_message = "";
            }
            if (ImGui::IsItemHovered()) {
                ImGui::SetMouseCursor(ImGuiMouseCursor_Hand);
            }
            ImGui::PopStyleColor();
        } else {
            // LOGIN UI with modern design
            ImGui::Spacing();

            // Section header
            const char* section_title = skCrypt("Welcome Back").decrypt();
            ImVec2 section_title_size = ImGui::CalcTextSize(section_title);
            ImGui::SetCursorPosX((content_width - section_title_size.x) * 0.5f);
            ImGui::TextColored(UIDesign::ACCENT_PRIMARY, "%s", section_title);

            ImGui::Spacing();
            ImGui::Spacing();

            float available_width = ImMin(item_width, content_width);
            float start_x = (content_width - available_width) * 0.5f;

            // Username input with modern styling
            ImGui::SetCursorPosX(start_x);
            ImGui::PushItemWidth(available_width);
            UIHelpers::ModernInputText(skCrypt("##username").decrypt(), skCrypt("Enter your username").decrypt(),
                                     username_buf, sizeof(username_buf), 0);
            ImGui::PopItemWidth();
            ImGui::Spacing();

            // Password input with show/hide toggle
            ImGui::SetCursorPosX(start_x);
            ImGui::PushItemWidth(available_width - 90.0f);
            UIHelpers::ModernInputText(skCrypt("##password").decrypt(), skCrypt("Enter your password").decrypt(),
                                     password_buf, sizeof(password_buf), 1, !show_password);
            ImGui::PopItemWidth();

            ImGui::SameLine();
            if (UIHelpers::ModernButton(show_password ? skCrypt("Hide").decrypt() : skCrypt("Show").decrypt(),
                                      ImVec2(80, 0), 2)) {
                show_password = !show_password;
            }

            ImGui::Spacing();
            ImGui::Spacing();

            // Login button with validation
            ImGui::SetCursorPosX(start_x);
            bool fields_valid = (strlen(username_buf) > 0 && strlen(password_buf) > 0);

            if (UIHelpers::ModernButton(skCrypt("Sign In").decrypt(), ImVec2(available_width, 45), 3, fields_valid) && fields_valid) {
                auth_error_message = "";
                strcpy_s(globals.user_name, username_buf);
                strcpy_s(globals.pass_word, password_buf);
                globals.login_in_progress = true;
                last_activity = std::chrono::steady_clock::now();
                std::thread(auth_thread_func, false, std::string(username_buf), std::string(password_buf), skCrypt("").decrypt()).detach();
            }

            ImGui::Spacing();
            ImGui::Spacing();

            // Switch to register link with modern styling
            std::string switch_text = skCrypt("Don't have an account? ").decrypt();
            std::string register_text = skCrypt("Create Account").decrypt();
            ImVec2 switch_size = ImGui::CalcTextSize(switch_text.c_str());
            ImVec2 register_size = ImGui::CalcTextSize(register_text.c_str());
            float total_width = switch_size.x + register_size.x;

            ImGui::SetCursorPosX((content_width - total_width) * 0.5f);
            ImGui::TextColored(UIDesign::TEXT_SECONDARY, "%s", switch_text.c_str());
            ImGui::SameLine(0, 0);

            ImGui::PushStyleColor(ImGuiCol_Text, UIDesign::ACCENT_PRIMARY);
            if (ImGui::Selectable(register_text.c_str(), false, ImGuiSelectableFlags_DontClosePopups, ImVec2(register_size.x, 0))) {
                show_register = true;
                auth_error_message = "";
                // Clear input fields when switching
                SecureZeroMemory(username_buf, sizeof(username_buf));
                SecureZeroMemory(password_buf, sizeof(password_buf));
            }
            if (ImGui::IsItemHovered()) {
                ImGui::SetMouseCursor(ImGuiMouseCursor_Hand);
                // Add hover glow effect
                ImVec2 item_pos = ImGui::GetItemRectMin();
                ImVec2 item_size = ImGui::GetItemRectSize();
                UIHelpers::DrawGlowBorder(item_pos, item_size, UIDesign::ACCENT_PRIMARY, 0.3f, 2.0f);
            }
            ImGui::PopStyleColor();
        }

        // Error message display with modern styling and effects
        if (!auth_error_message.empty()) {
            ImGui::Spacing();

            // Draw separator with gradient
            ImVec2 separator_pos = ImGui::GetCursorScreenPos();
            ImVec2 separator_size = ImVec2(content_width * 0.8f, 1.0f);
            separator_pos.x += (content_width - separator_size.x) * 0.5f;
            UIHelpers::DrawGradientRect(separator_pos, separator_size,
                                      ImVec4(0, 0, 0, 0), UIDesign::ACCENT_DANGER, true);

            ImGui::Spacing();

            // Error message with glow effect
            ImVec2 error_size = ImGui::CalcTextSize(auth_error_message.c_str());
            ImVec2 error_pos = ImGui::GetCursorScreenPos();
            error_pos.x += (content_width - error_size.x) * 0.5f;

            ImGui::SetCursorPosX((content_width - error_size.x) * 0.5f);

            // Error glow effect
            ImDrawList* draw_list = ImGui::GetWindowDrawList();
            for (int i = 0; i < 3; i++) {
                float alpha = 0.2f / (i + 1);
                ImU32 glow_color = ColorWithAlpha(UIDesign::ACCENT_DANGER, alpha);
                ImVec2 glow_offset = ImVec2(i * 0.5f, i * 0.5f);
                draw_list->AddText(ImVec2(error_pos.x + glow_offset.x, error_pos.y + glow_offset.y), glow_color, auth_error_message.c_str());
            }

            ImGui::TextColored(UIDesign::ACCENT_DANGER, "%s", auth_error_message.c_str());
        }
    }

    ImGui::End();

    // Pop all style modifications
    ImGui::PopStyleVar(12);
    ImGui::PopStyleColor(20);
}

void initialize(LPDIRECT3DDEVICE9 device) {
    dev = device;

    // Center window on screen
    RECT desktop;
    GetWindowRect(GetDesktopWindow(), &desktop);
    screen_res.x = (float)(desktop.right - desktop.left);
    screen_res.y = (float)(desktop.bottom - desktop.top);

    window_pos.x = (screen_res.x - window_size.x) / 2.0f;
    window_pos.y = (screen_res.y - window_size.y) / 2.0f;
}

void shutdown() {
    // Clean shutdown
}

} // namespace ui

// Session status and utility functions
std::string tm_to_readable_time(tm ctx) {
    char buffer[32];
    strftime(buffer, sizeof(buffer), "%m/%d/%Y %H:%M:%S", &ctx);
    return std::string(buffer);
}

static std::time_t string_to_timet(std::string timestamp) {
    auto cv = strtol(timestamp.c_str(), NULL, 10);
    return (time_t)cv;
}

static std::tm timet_to_tm(time_t timestamp) {
    std::tm context;
    localtime_s(&context, &timestamp);
    return context;
}

void sessionStatus() {
    INTEGRITY_CHECK();
    TIMING_CHECK();
    FLOW_OBFUSCATE();

    if (KeyAuthApp.user_data.subscriptions.empty()) {
        return;
    }

    auto subscription = KeyAuthApp.user_data.subscriptions.at(0);
    auto expiry = subscription.expiry;
    auto expiry_tm = timet_to_tm(string_to_timet(expiry));
    auto expiry_str = tm_to_readable_time(expiry_tm);

    #if defined(DEV)
    std::cout << skCrypt("Session expires: ").decrypt() << expiry_str << std::endl;
    #endif
}
